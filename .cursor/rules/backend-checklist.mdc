---
description: 
globs: 
alwaysApply: false
---
[] Using z validators zid, zaction, zquery, zmutation, zinternalquery, zinternalmutation
[] No v validators or Id (must use zid)
[] All types are in a [domain]Schema.ts file that is colocated. 
[] All args and returns are validated. Do not use 'output'.
[] Check for redundant types or redundant code  
[] Avoid explicit type annotations from handler parameters and let TypeScript infer them from the Zod schema
