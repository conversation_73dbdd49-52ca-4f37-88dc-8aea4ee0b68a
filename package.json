{"private": true, "type": "module", "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "generate": " ", "migrate": " ", "studio": " ", "seed:categories": "tsx scripts/seed-categories.ts", "lint": "eslint .", "lint:fix": "eslint --fix .", "format": "prettier --write .", "format:check": "prettier --check .", "cleanup": "tsx seedCleanupFunctions.ts"}, "dependencies": {"@ai-sdk/google": "^1.2.19", "@ai-sdk/openai": "^1.2.0", "@assistant-ui/react": "^0.8.9", "@assistant-ui/react-ai-sdk": "^0.8.0", "@assistant-ui/react-markdown": "^0.8.0", "@auth/core": "0.37.0", "@convex-dev/agent": "^0.1.8", "@convex-dev/aggregate": "^0.1.21", "@convex-dev/auth": "^0.0.80", "@convex-dev/migrations": "^0.2.9", "@convex-dev/prosemirror-sync": "^0.1.21", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hello-pangea/dnd": "^18.0.1", "@hookform/resolvers": "^3.10.0", "@neondatabase/serverless": "^0.9.5", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-hover-card": "^1.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-tooltip": "^1.1.8", "@tabler/icons-react": "^3.29.0", "@tanstack/react-table": "^8.20.6", "@tiptap/core": "^2.11.5", "@tiptap/extension-image": "^2.11.5", "@tiptap/extension-link": "^2.11.5", "@tiptap/extension-placeholder": "^2.11.5", "@tiptap/extension-table": "^2.11.7", "@tiptap/extension-table-cell": "^2.11.7", "@tiptap/extension-table-header": "^2.11.7", "@tiptap/extension-table-row": "^2.11.7", "@tiptap/extension-task-item": "^2.11.5", "@tiptap/extension-task-list": "^2.11.5", "@tiptap/extension-typography": "^2.11.5", "@tiptap/extension-underline": "^2.11.5", "@tiptap/pm": "^2.11.5", "@tiptap/react": "^2.11.5", "@tiptap/starter-kit": "^2.11.5", "@types/node": "20.17.6", "@types/node-fetch": "^2.6.12", "@types/react": "19.0.0", "@types/react-dom": "19.0.0", "@types/uuid": "^10.0.0", "@vercel/analytics": "^1.4.1", "@vercel/blob": "^0.27.1", "@vercel/postgres": "^0.10.0", "ai": "^4.3.16", "autoprefixer": "^10.4.20", "box-typescript-sdk-gen": "^1.16.0", "buffer": "^6.0.3", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "convex": "^1.24.8", "convex-helpers": "^0.1.94", "date-fns": "^4.1.0", "embla-carousel-react": "^8.5.2", "fuse.js": "^7.1.0", "geist": "^1.3.1", "jaro-winkler": "^0.2.8", "lucide-react": "^0.400.0", "motion": "^12.4.7", "next": "15.2.0", "next-themes": "^0.4.4", "nextjs": "link:@convex-dev/auth/nextjs", "node-fetch": "^3.3.2", "pdf-parse": "^1.1.1", "perfect-debounce": "^1.0.0", "postcss": "^8.4.49", "prettier": "^3.4.2", "prop-types": "^15.8.1", "prosemirror-state": "^1.4.3", "prosemirror-transform": "^1.10.3", "react": "^19.0.0", "react-day-picker": "9.7.0", "react-dom": "19.0.0", "react-dropzone": "^14.3.5", "react-hook-form": "^7.54.2", "react-icons": "^5.5.0", "react-markdown": "^10.1.0", "react-mentions": "^4.4.10", "react-zoom-pan-pinch": "^3.7.0", "recharts": "^2.15.1", "remark-gfm": "^4.0.1", "server": "link:@convex-dev/auth/server", "server-only": "^0.0.1", "sharp": "^0.33.5", "sonner": "^1.7.4", "state": "link:@tiptap/pm/state", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "transform": "link:@tiptap/pm/transform", "trieve-ts-sdk": "^0.0.71", "use-debounce": "^10.0.4", "voyageai": "^0.0.4", "zod": "^3.24.1"}, "prettier": {"arrowParens": "always", "singleQuote": true, "tabWidth": 2, "trailingComma": "none"}, "devDependencies": {"@next/eslint-plugin-next": "^15.2.0", "@stagewise/toolbar": "0.3.0-alpha.6", "@stagewise/toolbar-next": "0.2.0-alpha.6", "@tailwindcss/typography": "^0.5.16", "@types/canvas-confetti": "^1.9.0", "@types/react-mentions": "^4.4.1", "@typescript-eslint/eslint-plugin": "^8.25.0", "@typescript-eslint/parser": "^8.25.0", "dotenv": "^16.4.7", "eslint": "^9.21.0", "eslint-config-prettier": "^10.0.2", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.2.0", "sass": "^1.85.1", "tailwindcss": "^3.4.17", "ts-node": "^10.9.2", "tsx": "^4.19.2", "typescript": "5.7.2"}, "packageManager": "pnpm@9.14.4+sha512.c8180b3fbe4e4bca02c94234717896b5529740a6cbadf19fa78254270403ea2f27d4e1d46a08a0f56c89b63dc8ebfd3ee53326da720273794e6200fcf0d184ab"}