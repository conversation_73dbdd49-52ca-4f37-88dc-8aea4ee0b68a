'use client';

/**
 * This file displays the details of a specific Bill and includes:
 * - Overview of the bill (dates, amount, vendor info, memo)
 * - Line items associated with the bill
 * - Option to auto-categorize line items with AI
 * - Document preview if available
 *
 * Key Implementation Details:
 * - Hooks from Convex are used to query and update the database in real-time
 * - Local state is minimized; only used where strictly necessary (e.g., optimistic UI)
 * - React Hook rules are followed (all hooks at top-level, etc.)
 * - Redundant or unused code has been removed to improve clarity and efficiency
 * - Console logging has been minimized; any logs are purely for illustrative debugging
 * - UI is separated from database updates by using Convex queries/mutations under the hood
 */

import React, { use, useState, useCallback, useMemo } from 'react';
import { notFound } from 'next/navigation';
import { format } from 'date-fns';
import Link from 'next/link';
import {
  ArrowLeft,
  Calendar,
  DollarSign,
  Building,
  Mail,
  Phone,
  MapPin,
  User,
  Check
} from 'lucide-react';

import { api } from '@/convex/_generated/api';
import { Id } from '@/convex/_generated/dataModel';
import { useQuery, useMutation, useAction } from 'convex/react';
import { BillStatus } from '@/zod/bills-schema';

import { cn, stripHtml } from '@/lib/utils';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Popover, PopoverTrigger, PopoverContent } from '@/components/ui/popover';
import { Skeleton } from '@/components/ui/skeleton';
import { Input } from '@/components/ui/input';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { useToast } from '@/components/hooks/use-toast';
import { TiptapEditor } from '@/components/tiptap/TiptapEditor';
import { Calendar as CalendarComponent } from '@/components/ui/calendar';

import { DocumentPreview } from '@/components/document-preview';
import { BillLineItemsTable } from './bill-line-items-table';
import { AutoCategorizeButton } from '@/components/auto-categorize-button';

/* ------------------------------------------------------------------
  Skeleton components for loading states, ensuring good UX while data 
  queries are in progress.
------------------------------------------------------------------ */
function BillDetailsSkeleton() {
  return (
    <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
      <div className="space-y-1">
        <Skeleton className="h-4 w-16" />
        <Skeleton className="h-4 w-32" />
      </div>
      <div className="space-y-1">
        <Skeleton className="h-4 w-16" />
        <Skeleton className="h-9 w-full" />
      </div>
      <div className="space-y-1">
        <Skeleton className="h-4 w-16" />
        <Skeleton className="h-9 w-full" />
      </div>
    </div>
  );
}

function VendorInfoSkeleton() {
  return (
    <div className="mt-4 border-t pt-4">
      <Skeleton className="h-4 w-32 mb-3" />
      <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
        {[1, 2, 3, 4].map((i) => (
          <div
            key={i}
            className="flex items-center space-x-2 p-2 rounded-lg border border-border/50 bg-background"
          >
            <Skeleton className="h-4 w-4" />
            <div className="space-y-1">
              <Skeleton className="h-3 w-16" />
              <Skeleton className="h-4 w-24" />
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

/**
 * Main component displaying a detailed view of a bill.
 * Accepts 'params' from Next.js which contains the Bill ID.
 */
export default function BillDetailsPage({
  params
}: {
  params: Promise<{ id: string }>;
}) {
  const { toast } = useToast();

  // Tracks whether an AI categorization request is currently in progress
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Stores an optimistic version of the due date (for quick UI updates)
  const [optimisticDueDate, setOptimisticDueDate] = useState<number | null>(null);

  // Unwrap Next.js params for the Bill ID
  const unwrappedParams = use(params);
  const billId = unwrappedParams.id as Id<'bills'>;

  /* ------------------------------------------------------------------
    Convex Queries:
    - Bill data, line items, categories, vendor info, vendor members
  ------------------------------------------------------------------ */
  const billQuery = useQuery(api.bills.get, { id: billId });
  const lineItemsQuery = useQuery(api.lineItems.getLineItemsByBill, {
    bill_id: billId
  });
  const categoriesData = useQuery(api.tags.getHierarchy, { params: {} });
  const vendorQuery = useQuery(
    api.directory.directoryOrganizations.getOrganization,
    billQuery?.vendor_id ? { ids: [billQuery.vendor_id] } : 'skip'
  );
  const vendorMembersQuery = useQuery(
    api.directory.directoryOrganizations.listPeopleByOrganization,
    billQuery?.vendor_id ? { organizationId: billQuery.vendor_id } : 'skip'
  );

  // Query for associated files
  const filesQuery = useQuery(api.files.files.getByParentEntity, {
    parentEntityId: billId,
    docType: 'BILL'
  });

  // Convex Mutations
  const updateBill = useMutation(api.bills.update);
  
  // Status update handler
  const handleStatusChange = useCallback(
    async (newStatus: BillStatus) => {
      try {
        await updateBill({
          updates: {
            id: billId,
            updates: { billStatus: newStatus }
          }
        });
        toast({
          title: 'Status updated',
          description: `Bill status changed to ${newStatus}`,
        });
      } catch (error) {
        toast({
          title: 'Error updating status',
          description: 'There was an error updating the bill status.',
          variant: 'destructive'
        });
      }
    },
    [billId, updateBill, toast]
  );

  // Convex AI Action
  const runAiCategorize = useAction(api.actions.aiCategorizeAction.aiCategorize);

  // Categories data transformation to a simple array structure
  const categories = useMemo(() => {
    if (!categoriesData) return [];
    const arrayData = Array.isArray(categoriesData) ? categoriesData : [categoriesData];
    return arrayData.map((cat) => ({
      _id: cat._id,
      name: cat.name,
      tag_type: cat.tag_type,
      children: cat.children?.map((child) => ({
        _id: child._id,
        name: child.name,
        tag_type: child.tag_type
      })) || []
    }));
  }, [categoriesData]);

  /* ------------------------------------------------------------------
    Helper to handle successful AI-based categorization results
  ------------------------------------------------------------------ */
  const handleCategorizeSuccess = useCallback(
    async (result: {
      success: boolean;
      categorized: number;
      mappings: Record<string, { category: string }>;
    }) => {
      // Show a toast to the user indicating success
      toast({
        title: 'Categorization Complete',
        description: `Successfully categorized ${result.categorized} items`
      });

      // Since Convex queries update in real time, we can just turn off the refresh state
      setIsRefreshing(false);
    },
    [toast]
  );

  /* ------------------------------------------------------------------
    Database update for Bill fields. Called by other handlers below.
  ------------------------------------------------------------------ */
  const updateBillField = useCallback(
    async (field: 'amount' | 'dueDate' | 'memo', value: any) => {
      try {
        await updateBill({
          updates: {
            id: billId,
            updates: { [field]: value }
          }
        });
      } catch (error) {
        toast({
          title: `Error updating ${field}`,
          description: `There was an error updating the bill ${field}.`,
          variant: 'destructive'
        });
        throw error;
      }
    },
    [billId, updateBill, toast]
  );

  /* ------------------------------------------------------------------
    Handler for the Amount input field:
    - On blur, parse and save the numeric value.
    - Format the input with commas & decimals for display.
  ------------------------------------------------------------------ */
  const handleAmountBlur = useCallback(
    (e: React.FocusEvent<HTMLInputElement>) => {
      const val = e.target.value;
      const numeric = parseFloat(val);

      if (!isNaN(numeric)) {
        e.target.value = numeric.toLocaleString('en-US', {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2
        });
        updateBillField('amount', numeric);
      }
    },
    [updateBillField]
  );

  /* ------------------------------------------------------------------
    Handler for due date changes:
    - Sets an optimistic local state for immediate UI feedback.
    - Saves the new date to DB in the background.
  ------------------------------------------------------------------ */
  const handleDueDateChange = useCallback(
    (date: Date | undefined) => {
      if (date) {
        setOptimisticDueDate(date.getTime());

        // Close popover (hacky approach by clicking the open popover)
        const popover = document.querySelector('[data-state="open"]');
        if (popover) {
          (popover as HTMLElement).click();
        }

        // Save to database in the background, revert on error
        updateBillField('dueDate', date.getTime()).catch(() => {
          setOptimisticDueDate(null);
          toast({
            title: 'Error updating due date',
            description: 'There was an error saving the due date to the database.',
            variant: 'destructive'
          });
        });
      }
    },
    [updateBillField, toast]
  );

  /* ------------------------------------------------------------------
    Handler for memo/notes field (TiptapEditor):
    - Only saves to DB if the new content differs from the old
  ------------------------------------------------------------------ */
  const handleSaveMemo = useCallback(
    async (content: string) => {
      try {
        if (billQuery?.memo !== content) {
          await updateBill({
            updates: {
              id: billId,
              updates: { memo: content }
            }
          });
        }
      } catch (error) {
        console.error('Error saving memo:', error);
        toast({
          title: 'Error saving memo',
          description: 'There was an error saving your notes.',
          variant: 'destructive'
        });
        throw error;
      }
    },
    [billId, billQuery?.memo, updateBill, toast]
  );

  /* ------------------------------------------------------------------
    Loading and Not Found states:
    - Return skeleton if any main query is undefined (loading)
    - Return notFound() if the Bill itself is null
  ------------------------------------------------------------------ */
  if (billQuery === undefined || lineItemsQuery === undefined || categories === undefined) {
    return <BillDetailsSkeleton />;
  }
  if (billQuery === null) {
    notFound();
  }

  /* ------------------------------------------------------------------
    Data objects derived from queries:
    - Bill info, line items, vendor info, etc.
  ------------------------------------------------------------------ */
  const bill = billQuery;
  const lineItems = lineItemsQuery;
  const vendor = vendorQuery?.[0];
  const vendorMembers = Array.isArray(vendorMembersQuery)
    ? vendorMembersQuery
    : vendorMembersQuery?.people || [];

  // Basic typed fields
  const isCreditCardStatement = bill.type === 'CREDIT_CARD';
  const billDate = new Date(bill.billDate);
  const status = bill.billStatus || 'Unknown';

  // Vendor details
  const vendorName = vendor ? vendor.name : 'Unknown Vendor';
  const vendorDescription = vendor?.description ? stripHtml(vendor.description) : null;

  const vendorContact =
    vendorMembers.length > 0
      ? `${vendorMembers[0].name}`.trim()
      : null;
  const vendorEmail =
    vendor?.email || (vendorMembers.length > 0 ? vendorMembers[0].email : null);
  const vendorPhone =
    vendor?.phone || (vendorMembers.length > 0 ? vendorMembers[0].phone : null);

  // For due date display, use optimistic value if present
  const displayDueDate = optimisticDueDate !== null ? optimisticDueDate : bill.dueDate;

  /* ------------------------------------------------------------------
    Final JSX Layout
  ------------------------------------------------------------------ */
  return (
    <div className="container py-8 max-w-7xl">
      {/* ------------------------ Back Button ------------------------ */}
      <div className="mb-8">
        <Button variant="ghost" className="-ml-4" asChild>
          <Link href="/bills">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Bills
          </Link>
        </Button>
      </div>

      <div className="space-y-6">
        {/* -------------------- Bill Overview Card -------------------- */}
        <Card className="border rounded-lg shadow-sm">
          <CardHeader className="flex-row items-center justify-between space-y-0 pb-4 border-b">
            <div className="space-y-1">
              <CardTitle className="flex items-center gap-2">
                {isCreditCardStatement ? 'Credit Card Statement' : 'Bill'} #{bill.billNo}
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Badge
                      className={cn(
                        'ml-2 cursor-pointer',
                        status === 'PAID' &&
                          'bg-green-50 text-green-700 hover:bg-green-100',
                        status === 'UNPAID' &&
                          'bg-red-50 text-red-700 hover:bg-red-100',
                        status === 'PARTIALLY_PAID' &&
                          'bg-yellow-50 text-yellow-700 hover:bg-yellow-100',
                        status === 'SCHEDULED' &&
                          'bg-blue-50 text-blue-700 hover:bg-blue-100',
                        status === 'IN_PROCESS' &&
                          'bg-purple-50 text-purple-700 hover:bg-purple-100',
                        status === 'Unknown' &&
                          'bg-gray-50 text-gray-700 hover:bg-gray-100'
                      )}
                    >
                      {status}
                    </Badge>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    {['PAID', 'UNPAID', 'PARTIALLY_PAID', 'SCHEDULED', 'IN_PROCESS'].map((statusOption) => (
                      <DropdownMenuItem 
                        key={statusOption}
                        className={cn(
                          'flex items-center gap-2',
                          status === statusOption && 'font-bold'
                        )}
                        onClick={() => handleStatusChange(statusOption as BillStatus)}
                      >
                        {status === statusOption && <Check className="h-4 w-4" />}
                        <span className={status === statusOption ? 'ml-0' : 'ml-6'}>
                          {statusOption}
                        </span>
                      </DropdownMenuItem>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>
              </CardTitle>
              <div className="text-sm text-muted-foreground flex items-center">
                <span>From</span>
                {vendorQuery === undefined ? (
                  <Skeleton className="h-4 w-32 ml-1" />
                ) : vendor ? (
                  <Link
                    href={`/directory/organizations/${vendor._id}`}
                    className="ml-1 text-primary hover:underline flex items-center"
                  >
                    {vendorName}
                    <Building className="ml-1 h-3 w-3" />
                  </Link>
                ) : (
                  <span className="ml-1">{vendorName}</span>
                )}
              </div>
            </div>
          </CardHeader>

          <CardContent className="grid gap-6 pt-4">
            {/* ------------------ Bill Date, Due Date, Amount ------------------ */}
            <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
              {/* Bill Date */}
              <div className="space-y-1">
                <p className="text-sm font-medium leading-none">Bill Date</p>
                <p className="text-sm text-muted-foreground">
                  {format(billDate, 'MMMM d, yyyy')}
                </p>
              </div>

              {/* Due Date */}
              <div className="space-y-1">
                <p className="text-sm font-medium leading-none">Due Date</p>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full justify-start text-left font-normal"
                      size="sm"
                    >
                      <Calendar className="mr-2 h-4 w-4" />
                      {displayDueDate
                        ? format(new Date(displayDueDate), 'MMMM d, yyyy')
                        : 'Select a date'}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <CalendarComponent
                      mode="single"
                      selected={displayDueDate ? new Date(displayDueDate) : undefined}
                      onSelect={handleDueDateChange}
                      initialFocus
                      disabled={(date) => date < new Date()}
                      fixedWeeks
                      showOutsideDays={false}
                    />
                  </PopoverContent>
                </Popover>
              </div>

              {/* Amount */}
              <div className="space-y-1">
                <p className="text-sm font-medium leading-none">Amount</p>
                <div className="w-full">
                  <Input
                    type="text"
                    defaultValue={billQuery?.amount?.toString() ?? ''}
                    onBlur={handleAmountBlur}
                  />
                </div>
              </div>
            </div>

            {/* ------------------ Memo Field (TiptapEditor) ------------------ */}
            <div className="space-y-1 mt-4">
              <p className="text-sm font-medium leading-none">Memo</p>
              <TiptapEditor
                content={billQuery?.memo || ''}
                onChange={() => {
                  /** no-op; rely on onSave for DB updates */
                }}
                onSave={handleSaveMemo}
                variant="basic"
                placeholder="Add notes about this bill..."
                minHeight="80px"
                maxHeight="200px"
                autoExpand={false}
              />
            </div>

            {/* ------------------ Vendor Information ------------------ */}
            {vendorQuery === undefined ? (
              <VendorInfoSkeleton />
            ) : vendor ? (
              <div className="mt-4 border-t pt-4">
                <h3 className="text-sm font-medium mb-3">Vendor Information</h3>
                {vendorDescription && (
                  <div className="mb-4 p-3 bg-muted/30 rounded-lg">
                    <p className="text-sm text-muted-foreground">
                      {vendorDescription}
                    </p>
                  </div>
                )}
                <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
                  {vendorContact && (
                    <div className="flex items-center space-x-2 p-2 rounded-lg border border-border/50 bg-background">
                      <User className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <p className="text-xs font-medium">Contact</p>
                        <p className="text-sm">{vendorContact}</p>
                      </div>
                    </div>
                  )}

                  {vendorEmail && (
                    <div className="flex items-center space-x-2 p-2 rounded-lg border border-border/50 bg-background">
                      <Mail className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <p className="text-xs font-medium">Email</p>
                        <p className="text-sm">{vendorEmail}</p>
                      </div>
                    </div>
                  )}

                  {vendorPhone && (
                    <div className="flex items-center space-x-2 p-2 rounded-lg border border-border/50 bg-background">
                      <Phone className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <p className="text-xs font-medium">Phone</p>
                        <p className="text-sm">{vendorPhone}</p>
                      </div>
                    </div>
                  )}

                  {vendor.address && (
                    <div className="flex items-center space-x-2 p-2 rounded-lg border border-border/50 bg-background">
                      <MapPin className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <p className="text-xs font-medium">Address</p>
                        <p className="text-sm">{vendor.address}</p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            ) : null}
          </CardContent>
        </Card>

        {/* ------------------ Line Items Card ------------------ */}
        <Card className="border rounded-lg shadow-sm overflow-hidden">
          <CardContent className="p-0">
            {lineItems && (
              <div className="w-full">
                <div className="px-4 pt-4">
                  <AutoCategorizeButton
                    isLoading={isRefreshing}
                    onCategorize={async () => {
                      const uncategorizedItems = lineItems.filter(
                        (item) => !item.spending_category
                      );
                      if (uncategorizedItems.length === 0) return;

                      setIsRefreshing(true);

                      try {
                        const result = await runAiCategorize({
                          lineItems: uncategorizedItems.map((item) => ({
                            _id: item._id,
                            amount: item.amount,
                            description: item.description,
                            merchant_name: item.merchant_name
                          })),
                          skipDatabaseUpdate: false
                        });
                        handleCategorizeSuccess(result);
                      } catch (error) {
                        setIsRefreshing(false);
                        toast({
                          title: 'Error',
                          description:
                            error instanceof Error
                              ? error.message
                              : 'Failed to categorize items',
                          variant: 'destructive'
                        });
                      }
                    }}
                    itemCount={lineItems.filter(
                      (item) => !item.spending_category
                    ).length}
                  />
                </div>
                <BillLineItemsTable
                  isRefreshing={isRefreshing}
                  billId={billId}
                  totalAmount={bill.amount}
                  categories={categories || []}
                />
              </div>
            )}
          </CardContent>
        </Card>

        {/* ------------------ Document Preview ------------------ */}
        {filesQuery?.[0] && (
          <Card className="border rounded-lg shadow-sm">
            <DocumentPreview
              documentStorageId={filesQuery[0].fileStorageId || null}
              documentFilename={filesQuery[0].fileFilename ?? null}
              id={billId}
            />
          </Card>
        )}
      </div>
    </div>
  );
}
