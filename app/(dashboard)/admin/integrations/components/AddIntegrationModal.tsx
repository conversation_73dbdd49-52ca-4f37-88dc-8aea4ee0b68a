import { useState } from 'react';
import { useMutation } from 'convex/react';
import { api } from '@/convex/_generated/api';
import Modal from '@/components/ui/modal';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { CreateIntegrationInput } from '@/convex/integrations/integrationsSchema';
import { useToast } from '@/components/hooks/use-toast';

interface AddIntegrationModalProps {
  onClose: () => void;
}

export function AddIntegrationModal({ onClose }: AddIntegrationModalProps) {
  const [formData, setFormData] = useState<CreateIntegrationInput>({
    immutable_slug: '',
    status: 'NEEDS_SETUP',
    display_name: '',
    updated_at: Date.now()
  });
  
  const { toast } = useToast();
  const createIntegration = useMutation(api.integrations.integrations.createIntegration);
  
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };
  
  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };
  
  const handleSubmit = async () => {
    try {
      await createIntegration({
        integration: formData
      });
      
      toast({
        title: "Success",
        description: "Integration created successfully"
      });
      
      onClose();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create integration",
        variant: "destructive"
      });
    }
  };
  
  return (
    <Modal
      heading="Add New Integration"
      subheading="Connect your application with external services"
      onClose={onClose}
      ctaButton={{
        text: "Create Integration",
        onClick: handleSubmit
      }}
      secondaryCta={{
        text: "Cancel",
        onClick: onClose
      }}
    >
      <div className="grid gap-4">
        <div className="grid w-full items-center gap-1.5 mb-4">
          <Label htmlFor="display_name">Display Name</Label>
          <Input
            id="display_name"
            name="display_name"
            value={formData.display_name}
            onChange={handleChange}
            required
          />
        </div>
        
        <div className="grid w-full items-center gap-1.5 mb-4">
          <Label htmlFor="integration_type">Integration Type</Label>
          <Select
            value={formData.immutable_slug}
            onValueChange={(value) => handleSelectChange('immutable_slug', value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select integration type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="slack">Slack</SelectItem>
              <SelectItem value="jira">Jira</SelectItem>
              <SelectItem value="bill_com">Bill.com</SelectItem>
              <SelectItem value="github">GitHub</SelectItem>
              <SelectItem value="google_drive">Google Drive</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
    </Modal>
  );
}
