'use client';

import { useQuery } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { Id } from '@/convex/_generated/dataModel';

export const useOrganizationData = (organizationId: Id<'organizations'>) => {
  // Fetch the specific organization
  const organizationData = useQuery(api.directory.directoryOrganizations.getOrganization, { ids: [organizationId] });
  const organization = organizationData?.[0];

  // Fetch people associated with this organization
  const peopleData = useQuery(api.directory.directoryOrganizations.listPeopleByOrganization, { organizationId });
  const people = peopleData?.people ?? []; // Extract people array

  // Fetch notes for the organization
  const notes = useQuery(api.directory.directory.listDirectoryNote, {
    subject_type: 'ORGANIZATION',
    subject_id: organizationId
  });

  // Fetch organization tags using the new generic function
  const organizationTagsResult = useQuery(
    api.tags.getTagsForTaggable,
    organizationId ? {
      taggable_type: "organization",
      taggable_id: organizationId
    } : 'skip'
  );
  // Always provide a fallback for organizationTags to prevent rendering errors
  const organizationTags = Array.isArray(organizationTagsResult) ? organizationTagsResult : [];

  // Fetch documents linked to the organization with pagination options
  const documents = useQuery(api.relationships.fileRelationships.listFilesBySubject, {
    subject_type: 'organization',
    subject_id: organizationId,
    paginationOpts: { numItems: 10, cursor: null }
  });

  const relationshipType = useQuery(api.relationships.getRelationshipTypeByName, { name: "Employee" });

  return {
    organization,
    people,
    notes,
    organizationTags,
    documents,
    relationshipType,
    isLoading: organizationData === undefined || peopleData === undefined || notes === undefined || organizationTagsResult === undefined || documents === undefined || relationshipType === undefined,
  };
};
