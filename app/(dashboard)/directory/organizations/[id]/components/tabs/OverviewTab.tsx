'use client';

import { useOrganizationContext } from '../../context/OrganizationContext';
import { TiptapEditor } from '@/components/tiptap/TiptapEditor';

export const OverviewTab = () => {
  const { organization, handleSaveDescription, handleUpdateField } = useOrganizationContext();

  if (!organization) {
    return null; // Or a loading state
  }

  return (
    <div className="space-y-8">
      <div>
        <h2 className="text-lg font-medium mb-4">Description</h2>
        <TiptapEditor
          content={organization.description ?? ''}
          onChange={() => {}}
          onSave={handleSaveDescription}
          variant="basic"
          placeholder="Add a description..."
        />
      </div>
      
      <div>
        <h2 className="text-lg font-medium mb-4">Research</h2>
        <TiptapEditor
          content={organization.research ?? ''}
          onChange={() => {}}
          onSave={(value) => handleUpdateField('research', value)}
          variant="basic"
          placeholder="Add research information..."
        />
      </div>
    </div>
  );
};
