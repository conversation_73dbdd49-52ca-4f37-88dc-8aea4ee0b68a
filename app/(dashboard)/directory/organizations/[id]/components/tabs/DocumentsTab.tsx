'use client';

import { useOrganizationContext } from '../../context/OrganizationContext';
import { SectionHeader } from '../SectionHeader';
import { EmptyState } from '../EmptyState';
// import { ListItem } from '../ListItem'; // Import when ready to list documents

export const DocumentsTab = () => {
  const { documents } = useOrganizationContext();

  return (
    <div>
      <SectionHeader title="Documents" />
      {documents && documents.page.length > 0 ? (
        <div className="space-y-3">
          {/* TODO: Map over documents and render ListItem when data structure is finalized */}
          <p className="text-muted-foreground">
            Documents will be listed here.
          </p>
        </div>
      ) : (
        <EmptyState message="No documents linked to this organization yet." />
      )}
    </div>
  );
};
