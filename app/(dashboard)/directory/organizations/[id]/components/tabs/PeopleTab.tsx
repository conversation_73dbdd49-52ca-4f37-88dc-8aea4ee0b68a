'use client';

import { useOrganizationContext } from '../../context/OrganizationContext';
import { AssignPersonPopover } from '@/components/directory/AssignPersonPopover';
import { ListItem } from '../ListItem';
import { EmptyState } from '../EmptyState';
import { SectionHeader } from '../SectionHeader';
import { ConfirmationDialog } from '../ConfirmationDialog';
import { Button } from '@/components/ui/button';
import { Trash2 } from 'lucide-react';
import { Id } from '@/convex/_generated/dataModel';

export const PeopleTab = () => {
  const { organization, people, handleDeletePersonFromOrg } = useOrganizationContext();

  if (!organization) {
    return null; // Or a loading state
  }

  return (
    <div>
      <SectionHeader
        title="People"
        actions={<AssignPersonPopover organizationId={organization._id} />}
      />
      <div className="space-y-3">
        {people.length > 0 ? (
          people.map((p) => (
            <ListItem
              key={p._id}
              id={p._id}
              href={`/directory/people/${p._id}`}
              imageUrl={p.image}
              fallbackText={p.name?.[0] ?? ''}
              title={p.name ?? 'Unnamed'}
              subtitle={p.title ?? undefined}
              actions={
                <ConfirmationDialog
                  trigger={
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0 text-muted-foreground hover:text-destructive"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  }
                  title="Remove Person"
                  description={`Are you sure you want to remove ${p.name} from this organization? This action cannot be undone.`}
                  onConfirm={() => handleDeletePersonFromOrg(p._id as Id<'people'>)}
                  confirmText="Remove"
                />
              }
            />
          ))
        ) : (
          <EmptyState message="No people associated yet." />
        )}
      </div>
    </div>
  );
};
