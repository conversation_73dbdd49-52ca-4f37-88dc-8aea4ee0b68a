'use client';

import { useState } from 'react';
import { useOrganizationContext } from '../context/OrganizationContext';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { ConfirmationDialog } from './ConfirmationDialog';
import { FileText, Activity, Plus, MoreVertical, Trash2 } from 'lucide-react';
import { z } from 'zod';
import { DirectoryNoteSchema } from '@/zod/directory-schema';
import { Id } from '@/convex/_generated/dataModel';

export const OrganizationNotesActivity = () => {
  const { notes, handleCreateNote, handleDeleteNote } = useOrganizationContext();
  const [showNoteInput, setShowNoteInput] = useState(false);
  const [newNote, setNewNote] = useState('');

  const onNoteCreate = () => {
    handleCreateNote(newNote, () => {
      setNewNote('');
      setShowNoteInput(false);
    });
  };

  return (
    <ScrollArea className="w-[250px] shrink-0">
      <div className="p-2 mt-4">
        <Tabs defaultValue="notes" className="w-full">
          <TabsList className="mb-4 w-full">
            <TabsTrigger value="notes" className="flex items-center gap-2 flex-1">
              <FileText className="w-4 h-4" />
              Notes
            </TabsTrigger>
            <TabsTrigger value="activity" className="flex items-center gap-2 flex-1">
              <Activity className="w-4 h-4" />
              Activity
            </TabsTrigger>
          </TabsList>

          <TabsContent value="notes">
            <div>
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-lg font-medium">Your Notes</h2>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowNoteInput(true)}
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Add Note
                </Button>
              </div>

              {showNoteInput && (
                <div className="space-y-2 mb-6">
                  <textarea
                    value={newNote}
                    onChange={(e) => setNewNote(e.target.value)}
                    className="min-h-[100px] w-full p-2 text-sm border rounded-md"
                    placeholder="Add a new note..."
                  />
                  <div className="flex justify-end gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setNewNote('');
                        setShowNoteInput(false);
                      }}
                    >
                      Cancel
                    </Button>
                    <Button size="sm" onClick={onNoteCreate}>
                      Save Note
                    </Button>
                  </div>
                </div>
              )}

              <div className="space-y-3">
                {notes?.notes?.map((note: z.infer<typeof DirectoryNoteSchema>) => (
                  <div key={note._id} className="p-2 rounded-lg border bg-card">
                    <p className="mb-1 text-xs">{note.content}</p>
                    <div className="flex items-center justify-between mt-1 text-xs text-muted-foreground">
                      <span>Added {new Date(note._creationTime).toLocaleDateString()}</span>
                      <ConfirmationDialog
                        trigger={
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="icon" className="h-6 w-6">
                                <MoreVertical className="h-4 w-4" />
                                <span className="sr-only">Note options</span>
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                                <DropdownMenuItem className="text-destructive focus:text-destructive focus:bg-destructive/10">
                                  <Trash2 className="mr-2 h-4 w-4" />
                                  Delete
                                </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        }
                        title="Delete Note"
                        description="Are you sure you want to delete this note? This action cannot be undone."
                        onConfirm={() => handleDeleteNote(note._id as Id<'directory_notes'>)}
                        confirmText="Delete"
                      />
                    </div>
                  </div>
                ))}
                {(!notes || !notes.notes || notes.notes.length === 0) && (
                  <div className="text-xs text-muted-foreground text-center py-2">
                    No notes yet
                  </div>
                )}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="activity">
            <div>
              <h2 className="text-lg font-medium mb-4">Activity</h2>
              <p className="text-muted-foreground">
                Recent activity will be displayed here.
              </p>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </ScrollArea>
  );
};
