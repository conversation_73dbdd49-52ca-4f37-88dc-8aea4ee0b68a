'use client';

import { ReactNode } from 'react';
import Link from 'next/link';
import { cn } from '@/lib/utils';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

interface ListItemProps {
  id: string;
  href: string;
  imageUrl?: string;
  fallbackText: string;
  title: string;
  subtitle?: string;
  actions?: ReactNode;
  className?: string;
}

export const ListItem = ({
  id,
  href,
  imageUrl,
  fallbackText,
  title,
  subtitle,
  actions,
  className,
}: ListItemProps) => {
  return (
    <div
      key={id}
      className={cn(
        'flex items-center justify-between p-3 rounded-lg border bg-muted/10 hover:bg-muted/20 transition-colors',
        className
      )}
    >
      <div className="flex items-center gap-3">
        <Avatar className="h-9 w-9">
          <AvatarImage src={imageUrl} alt={title} />
          <AvatarFallback>{fallbackText}</AvatarFallback>
        </Avatar>
        <div>
          <Link href={href} className="font-medium text-sm hover:underline">
            {title}
          </Link>
          {subtitle && (
            <div className="text-xs text-muted-foreground">{subtitle}</div>
          )}
        </div>
      </div>
      {actions && <div>{actions}</div>}
    </div>
  );
};
