'use client';

import { ReactNode } from 'react';
import { cn } from '@/lib/utils';

interface SectionHeaderProps {
  title: string;
  actions?: ReactNode;
  className?: string;
}

export const SectionHeader = ({ title, actions, className }: SectionHeaderProps) => {
  return (
    <div className={cn('flex items-center justify-between mb-4', className)}>
      <h2 className="text-lg font-medium">{title}</h2>
      {actions && <div>{actions}</div>}
    </div>
  );
};
