'use client';

import { useOrganizationContext } from '../context/OrganizationContext';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Eye, Users, FileText } from 'lucide-react';
import DocumentSummaryDisplay from '@/components/documents/DocumentSummaryDisplay';
import { OverviewTab } from './tabs/OverviewTab';
import { PeopleTab } from './tabs/PeopleTab';
import { DocumentsTab } from './tabs/DocumentsTab';

export const OrganizationMainContent = () => {
  const { organization } = useOrganizationContext();

  return (
    <ScrollArea className="flex-1 border-r">
      <div className="p-6">
        {organization?.short_description && (
          <DocumentSummaryDisplay summary={organization.short_description} />
        )}
        
        <Tabs defaultValue="overview" className="w-full">
          <TabsList className="mb-4 w-full">
            <TabsTrigger value="overview" className="flex items-center gap-2">
              <Eye className="w-4 h-4" />
              Overview
            </TabsTrigger>
            <TabsTrigger value="people" className="flex items-center gap-2">
              <Users className="w-4 h-4" />
              People
            </TabsTrigger>
            <TabsTrigger value="documents" className="flex items-center gap-2">
              <FileText className="w-4 h-4" />
              Documents
            </TabsTrigger>
          </TabsList>

          <TabsContent value="overview">
            <OverviewTab />
          </TabsContent>

          <TabsContent value="people">
            <PeopleTab />
          </TabsContent>

          <TabsContent value="documents">
            <DocumentsTab />
          </TabsContent>
        </Tabs>
      </div>
    </ScrollArea>
  );
};
