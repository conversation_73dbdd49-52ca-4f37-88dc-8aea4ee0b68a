'use client';

import Link from 'next/link';
import { useOrganizationContext } from '../context/OrganizationContext';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { InlineEditField } from '@/components/ui/inline-edit-field';
import { AddOrganizationTagPopover } from '@/components/ui/AddOrganizationTagPopover';
import { formatPhoneNumber, cn } from '@/lib/utils';
import { ArrowLeft, Mail, Phone, MessageSquare, X as XIcon } from 'lucide-react';
import { Tag } from '@/zod/tags-schema';
import { Id } from '@/convex/_generated/dataModel';

export const OrganizationSidebar = () => {
  const { organization, organizationTags, handleUpdateField, handleRemoveTag } = useOrganizationContext();

  if (!organization) {
    return (
      <ScrollArea className="w-[250px] shrink-0 border-r">
        <div className="p-6">Loading...</div>
      </ScrollArea>
    );
  }

  return (
    <ScrollArea className="w-[250px] shrink-0 border-r">
      <div className="p-6 relative">
        <Link href="/directory">
          <Button
            variant="ghost"
            size="icon"
            className="absolute top-4 left-4 h-8 w-8 rounded-full z-10"
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
        </Link>
        
        <div className="pt-10 flex flex-col items-center text-center">
          <Avatar className="w-24 h-24 mb-4 bg-muted flex items-center justify-center">
            <AvatarFallback className="text-2xl">
              {organization.name[0]}
            </AvatarFallback>
          </Avatar>
          <div className="relative group w-full">
            <InlineEditField
              value={organization.name}
              onSave={(value) => handleUpdateField('name', value)}
              inputClassName="text-2xl font-semibold text-center w-full h-10"
              displayClassName="text-2xl font-semibold text-center w-full"
              label=""
              className="mb-1 text-center"
            />
          </div>

          <div className="flex gap-2 mt-3">
            <Badge className="bg-green-100 text-green-800 hover:bg-green-100">
              Active
            </Badge>
            {organization.is_vendor && (
              <Badge variant="secondary" className="bg-orange-100 text-orange-700 hover:bg-orange-100">
                Vendor
              </Badge>
            )}
          </div>

          <div className="flex gap-3 mt-6">
            {organization.website && (
              <Button variant="outline" size="icon" className="rounded-full w-10 h-10" asChild>
                <a href={organization.website.startsWith('http') ? organization.website : `https://${organization.website}`} target="_blank" rel="noopener noreferrer">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m0 0a9 9 0 019-9m-9 9a9 9 0 009 9" /></svg>
                </a>
              </Button>
            )}
            {organization.email && (
              <Button variant="outline" size="icon" className="rounded-full w-10 h-10" asChild>
                <a href={`mailto:${organization.email}`}>
                  <Mail className="w-4 h-4" />
                </a>
              </Button>
            )}
            {organization.phone && (
              <Button variant="outline" size="icon" className="rounded-full w-10 h-10" asChild>
                <a href={`tel:${organization.phone}`}>
                  <Phone className="w-4 h-4" />
                </a>
              </Button>
            )}
            <Button variant="outline" size="icon" className="rounded-full w-10 h-10">
              <MessageSquare className="w-4 h-4" />
            </Button>
          </div>
        </div>

        <Separator className="my-6" />

        <div>
          <h3 className="text-sm font-medium mb-4">Organization Information</h3>
          <div className="space-y-4">
            <InlineEditField
              label="Website"
              value={organization.website || ''}
              onSave={(value) => handleUpdateField('website', value)}
            />
            <InlineEditField
              label="Address"
              value={organization.address || ''}
              onSave={(value) => handleUpdateField('address', value)}
            />
             <InlineEditField
              label="Email"
              value={organization.email || ''}
              onSave={(value) => handleUpdateField('email', value)}
            />
            <InlineEditField
              label="Phone"
              value={organization.phone || ''}
              formatValue={formatPhoneNumber}
              onSave={(value) => handleUpdateField('phone', value)}
            />
              <div className="flex flex-col space-y-1">
                <div className="text-sm text-muted-foreground">Industry</div>
                <div className="text-sm">{'Finance & Investments'}</div>
              </div>
            </div>
          </div>

         <Separator className="my-6" />

        <div>
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-sm font-medium">Tags</h3>
            <AddOrganizationTagPopover organizationId={organization._id} />
          </div>
          <div className="flex flex-wrap gap-2">
            {organizationTags?.map((tag: Tag) => (
              <Badge
                key={tag._id}
                className={cn(
                  "relative group pr-6",
                  tag.color ? `${tag.color} hover:${tag.color}` : 'bg-secondary text-secondary-foreground hover:bg-secondary/80'
                )}
              >
                {tag.name}
                <button
                  onClick={() => handleRemoveTag(tag._id as Id<'tags'>)}
                  className="absolute top-1/2 right-1 transform -translate-y-1/2 p-0.5 rounded-full bg-background/50 hover:bg-background/70 opacity-0 group-hover:opacity-100 transition-opacity"
                  aria-label={`Remove tag ${tag.name}`}
                >
                  <XIcon className="h-2.5 w-2.5" />
                </button>
              </Badge>
            ))}
            {(!organizationTags || organizationTags.length === 0) && (
               <div className="text-xs text-muted-foreground">No tags assigned.</div>
            )}
          </div>
        </div>
      </div>
    </ScrollArea>
  );
};
