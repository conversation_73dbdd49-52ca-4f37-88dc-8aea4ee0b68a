'use client';

import { createContext, useContext, ReactNode } from 'react';
import { useOrganizationData } from '../hooks/useOrganizationData';
import { useOrganizationMutations } from '../hooks/useOrganizationMutations';
import { Id } from '@/convex/_generated/dataModel';

// Infer the return types from the hooks
type OrganizationDataContextType = ReturnType<typeof useOrganizationData>;
type OrganizationMutationsContextType = ReturnType<typeof useOrganizationMutations>;

// Combine them for the full context value
type OrganizationContextType = OrganizationDataContextType & OrganizationMutationsContextType;

// Create the context with a default undefined value
const OrganizationContext = createContext<OrganizationContextType | undefined>(undefined);

// Create the provider component
export const OrganizationProvider = ({
  organizationId,
  children,
}: {
  organizationId: Id<'organizations'>;
  children: ReactNode;
}) => {
  const data = useOrganizationData(organizationId);
  const mutations = useOrganizationMutations(organizationId, data.relationshipType?._id as Id<'relationship_types'> | undefined);

  const value = { ...data, ...mutations };

  return (
    <OrganizationContext.Provider value={value}>
      {children}
    </OrganizationContext.Provider>
  );
};

// Create the consumer hook
export const useOrganizationContext = () => {
  const context = useContext(OrganizationContext);
  if (context === undefined) {
    throw new Error('useOrganizationContext must be used within an OrganizationProvider');
  }
  return context;
};
