"use client";

import { useAction, useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";
import { Doc } from "@/convex/_generated/dataModel";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { DownloadIcon, FileIcon, FileTextIcon, SendIcon } from "lucide-react";
import { useEffect, useState } from "react";
import { Id } from "@/convex/_generated/dataModel";

// Helper function to determine file type from filename
const getFileType = (fileName: string | undefined | null) => {
    if (!fileName || typeof fileName !== 'string') {
        return 'other';
    }
    const extension = fileName.split('.').pop()?.toLowerCase();
    if (['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'].includes(extension || '')) {
      return 'image';
    }
    if (['pdf'].includes(extension || '')) {
      return 'pdf';
    }
    if (['mp4', 'mov', 'avi', 'webm'].includes(extension || '')) {
      return 'video';
    }
    if (['txt', 'md', 'json', 'xml', 'csv'].includes(extension || '')) {
      return 'text';
    }
    return 'other';
};

// Helper function to render file preview with thumbnails
const renderFilePreview = (doc: { url: string | null, fileName: string }) => {
    const fileType = getFileType(doc.fileName);
    
    if (!doc.url) {
      return (
        <div className="w-full aspect-[3/2] bg-gray-100 rounded-md flex items-center justify-center">
          <div className="text-center">
            <FileIcon className="h-6 w-6 text-gray-400 mx-auto mb-1" />
            <span className="text-xs text-gray-500">No preview available</span>
          </div>
        </div>
      );
    }

    switch (fileType) {
      case 'image':
        return (
          <div className="w-full aspect-[3/2] bg-gray-100 rounded-md overflow-hidden relative group">
            <img 
              src={doc.url} 
              alt={doc.fileName}
              className="w-full h-full object-cover transition-transform duration-200 group-hover:scale-105"
            />
            <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors duration-200" />
          </div>
        );
      
      case 'pdf':
        return (
          <div className="w-full aspect-[3/2] bg-gradient-to-br from-red-50 to-red-100 rounded-md relative overflow-hidden group">
            <div className="absolute inset-0">
              <iframe
                src={`${doc.url}#view=FitH&toolbar=0&navpanes=0&scrollbar=0&zoom=120`}
                className="w-full h-full border-0 pointer-events-none"
                title={`${doc.fileName} thumbnail`}
                style={{ transform: 'scale(1.2)', transformOrigin: 'top left' }}
              />
            </div>
            <div className="absolute inset-0 flex items-center justify-center bg-gradient-to-br from-red-50/90 to-red-100/90 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
              <div className="text-center">
                <FileTextIcon className="h-8 w-8 text-red-500 mx-auto mb-1" />
                <span className="text-sm text-red-700 font-medium">PDF</span>
              </div>
            </div>
          </div>
        );
      
      default:
        return (
          <div className="w-full aspect-[3/2] bg-gradient-to-br from-gray-50 to-gray-100 rounded-md flex items-center justify-center group hover:from-gray-100 hover:to-gray-200 transition-colors duration-200">
            <div className="text-center">
              <FileIcon className="h-8 w-8 text-gray-500 mx-auto mb-1" />
              <span className="text-sm text-gray-600 font-medium">
                {doc.fileName?.split('.').pop()?.toUpperCase() || 'FILE'}
              </span>
            </div>
          </div>
        );
    }
};


export function DocumentCard({ doc, setPreviewDocument, setSignRequestDocument, handleDownload }: { 
    doc: { 
        _id: Id<string>, 
        fileName: any, 
        url: string | null, 
        _creationTime: any,
        documentId: Id<string> | null,
        box_sign_request_id: any
    }, 
    setPreviewDocument: (doc: { 
        _id: Id<string>, 
        fileName: any, 
        url: string | null, 
        _creationTime: any,
        documentId: Id<string> | null,
        box_sign_request_id: any
    }) => void, 
    setSignRequestDocument: (doc: { 
        _id: Id<string>, 
        fileName: any, 
        url: string | null, 
        _creationTime: any,
        documentId: Id<string> | null,
        box_sign_request_id: any
    }) => void, 
    handleDownload: (url: string, fileName: string) => void 
}) {
    const getSignRequestStatus = useAction(api.box.boxSignatureActions.getSignRequestStatus);
    const [status, setStatus] = useState<{ status: string } | null>(null);

    useEffect(() => {
        if (doc.box_sign_request_id) {
            void getSignRequestStatus({ signRequestId: doc.box_sign_request_id }).then(setStatus);
        }
    }, [doc.box_sign_request_id, getSignRequestStatus]);

    return (
        <Card 
            key={doc._id} 
            className="hover:shadow-md transition-shadow cursor-pointer"
            onClick={() => doc.url && setPreviewDocument(doc)}
        >
            <CardContent className="p-4">
                {renderFilePreview({ ...doc, fileName: doc.fileName })}
                
                <div className="mt-3">
                    <div className="flex items-center justify-between mb-2">
                        <h3 className="font-medium text-sm truncate" title={doc.fileName}>
                            {doc.fileName}
                        </h3>
                        <div className="flex items-center">
                            <Button
                                variant="ghost"
                                size="icon"
                                onClick={(e) => {
                                    e.stopPropagation();
                                    setSignRequestDocument(doc);
                                }}
                                title="Send for Signature"
                            >
                                <SendIcon className="h-4 w-4" />
                            </Button>
                            {doc.url ? (
                                <Button
                                    variant="ghost"
                                    size="icon"
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        e.preventDefault();
                                        if (doc.url) {
                                            handleDownload(doc.url, doc.fileName);
                                        }
                                    }}
                                    className="flex items-center gap-1"
                                    title="Download"
                                >
                                    <DownloadIcon className="h-4 w-4" />
                                </Button>
                            ) : (
                                <span className="text-xs text-red-500">No URL</span>
                            )}
                        </div>
                    </div>
                    <p className="text-xs text-gray-500">
                        Added: {new Date(doc._creationTime).toLocaleDateString()}
                    </p>
                    {status && (
                        <p className="text-xs text-gray-500 mt-1">
                            Status: <span className="font-medium">{status.status}</span>
                        </p>
                    )}
                    {doc.url && !status && (
                        <p className="text-xs text-blue-600 mt-1">
                            Click to preview
                        </p>
                    )}
                </div>
            </CardContent>
        </Card>
    );
}
