g# Technical Context

## Technology Stack

FOJO is built using a modern web technology stack:

### Frontend

- **Framework**: Next.js 15 with App Router
- **UI Library**: React 19
- **Styling**: Tailwind CSS with shadcn/ui components
- **State Management**: React hooks, Context API, and Convex real-time queries
- **Form Handling**: React Hook Form with Zod validation
- **Data Tables**: TanStack Table
- **Rich Text Editing**: TipTap (Note: The `onSave` prop handles debounced autosaving internally; avoid redundant external save logic for content changes.)
- **Drag and Drop**: @dnd-kit and @hello-pangea/dnd
- **Charts and Visualization**: Recharts
- **Icons**: Lucide React and Tabler Icons
- **Date Handling**: date-fns
- **Search**: Fuse.js for client-side search

### Backend

- **Database**: Convex (document-based with real-time capabilities)
- **Authentication**: convex Auth
- **File Storage**: Convex
- **Serverless Functions**: Convex functions (using `zQuery`/`zMutation` wrappers for standardized validation)
- **Schema Validation**: Zod (used within `zQuery`/`zMutation` and potentially directly in actions)
- **Type Safety**: TypeScript (enhanced by Zod schema inference, though `any` type used in some mapping functions like in `trieveActions.ts` for flexibility)
- **File Naming Convention**: Use camelCase for Convex file names (e.g., `userActions.ts`, `projectQueries.ts`). Avoid dots or other separators. Module names can only contain alphanumeric characters and underscores.

### AI Integration

- **AI SDK**: Vercel AI SDK
- **AI Providers**: Google AI (Gemini)
- **Retrieval**: Trieve (for RAG)
  - **API Endpoint Used**: `POST /api/chunk/search`
  - **Credentials**: Requires `TRIEVE_API_KEY` and `TRIEVE_DATASET_ID` environment variables in Convex.
  - **Search Type**: Hybrid
- **Real-time Transcription**: OpenAI Realtime API (`gpt-4o-mini-transcribe` or similar) via WebRTC
  - **Token Generation**: Uses `POST /v1/realtime/transcription_sessions` via Convex HTTP action (`/openai/realtime/transcription/session`). Called directly from frontend (CORS handled).
  - **Connection**: Uses `POST /v1/realtime` via client-side WebRTC APIs using the ephemeral token.
  - **Credentials**: Requires `OPENAI_API_KEY` environment variable in Convex.
- **Real-time Conversation**: *(Newly Added)* OpenAI Realtime API (`gpt-4o-realtime-preview` or similar) via WebRTC
  - **Token Generation**: Uses `POST /v1/realtime/sessions` via Convex HTTP action (`/openai/realtime/conversation/session`). Called directly from frontend (CORS handled).
  - **Connection**: Uses `POST /v1/realtime` via client-side WebRTC APIs using the ephemeral token.
  - **Function Calling**: Configured via `session.update` event after connection. Handled by client-side logic receiving `response.done` events with `function_call` type and triggering Convex mutations directly via `useMutation` hook.
  - **Credentials**: Requires `OPENAI_API_KEY` environment variable in Convex.
- **Text Extraction**: PDF parsing for document analysis (Potentially handled by Trieve upload process)

### DevOps

- **Deployment**: Vercel
- **Analytics**: Vercel Analytics
- **Package Management**: pnpm
- **Linting**: ESLint
- **Formatting**: Prettier
- **Build Tool**: Turbopack

## Key Dependencies

From the package.json, the following are the core dependencies:

```json
{
  "dependencies": {
    "@ai-sdk/google": "^1.1.19",
    "@ai-sdk/openai": "^1.2.0",
    "@auth/core": "0.37.0",
    "@convex-dev/aggregate": "^0.1.20",
    "@convex-dev/auth": "^0.0.80",
    "@convex-dev/prosemirror-sync": "^0.1.13",
    "@dnd-kit/core": "^6.3.1",
    "@dnd-kit/modifiers": "^9.0.0",
    "@dnd-kit/sortable": "^10.0.0",
    "@dnd-kit/utilities": "^3.2.2",
    "@hello-pangea/dnd": "^18.0.1",
    "@hookform/resolvers": "^3.10.0",
    "@radix-ui/react-*": "various components",
    "@tanstack/react-table": "^8.20.6",
    "@tiptap/react": "^2.11.5",
    "convex": "^1.20.0",
    "date-fns": "^4.1.0",
    "fuse.js": "^7.1.0",
    "lodash": "^4.17.21",
    "next": "15.2.0",
    "react": "^19.0.0",
    "react-hook-form": "^7.54.2",
    "react-mentions": "^4.4.10",
    "recharts": "^2.15.1",
    "tailwindcss": "^3.4.17",
    "zod": "^3.24.1"
  }
}
```

## Development Environment

### Local Development Setup

1. **Node.js**: Required for running the Next.js application
2. **pnpm**: Package manager (version 9.14.4+)
3. **Environment Variables**: Configuration via `.env` file
4. **Convex CLI**: For managing the Convex database

### Development Commands

```bash
# Start development server with Turbopack
pnpm dev

# Build for production
pnpm build

# Start production server
pnpm start

# Linting
pnpm lint
pnpm lint:fix

# Formatting
pnpm format
pnpm format:check

# Database cleanup
pnpm cleanup
```

### Environment Variables

The application requires several environment variables, as indicated in `.env.example`:

- Authentication configuration
- API keys for external services
- Database connection details
- File storage configuration

## Project Structure

The project follows a structured organization:

```
/
├── app/                    # Next.js App Router pages and layouts
│   ├── (auth)/             # Authentication-related routes
│   ├── (dashboard)/        # Main application dashboard routes (e.g., /projects, /tasks/[id]/overview)
│   ├── components/         # App-specific components (rarely used, prefer /components)
│   ├── welcome/            # Onboarding and welcome pages
│   ├── layout.tsx          # Root layout
│   └── page.tsx            # Root page
├── components/             # Shared components
│   ├── blocks/             # Composite UI blocks
│   ├── cards/              # Card components
│   ├── decisions/          # Decision-specific components (e.g., ActionBar)
│   ├── documents/          # Document-specific components
│   ├── projects/           # Project-specific components (e.g., ActionBar)
│   ├── tasks/              # Task-specific components (e.g., ActionBar)
│   ├── hooks/              # Custom React hooks
│   ├── lib/                # Shared utility functions (non-UI)
│   └── ui/                 # Base UI components (atoms: Button, Card, Input, etc.)
├── convex/                 # Convex backend functions and schema
│   ├── _generated/         # Auto-generated Convex types
│   ├── actions/            # Convex actions (can call mutations/queries)
│   ├── schema.ts           # Database schema definition
│   └── clients/            # Example of a domain-specific folder
│       ├── clientMutations.ts
│       ├── clientQueries.ts
│       └── clientTypes.ts    # Co-located Zod schemas
├── lib/                    # Shared utility functions (client/server agnostic)
├── public/                 # Static assets
├── types/                  # TypeScript type definitions
```
>>>>>>> REPLACE

## Database Schema

The Convex database schema (`convex/schema.ts`) defines the following main tables:

- **bills**: Financial transactions and invoices
- **badges**: Visual indicators for various entities
- **tags**: Categorization system for entities
- **lineItems**: Individual line items for bills
- **roles**: User roles for permissions
- **users**: User accounts and authentication
- **people**: Person profiles (may be linked to users)
- **organizations**: Company and vendor information
- **files**: Document storage and metadata
  - **projects**: Project management entities (Includes DCI fields: `driver`, `contributors`, `informed`)
  - **decisions**: Decision tracking and documentation (Includes DCI fields: `driver`, `contributors`, `informed`)
  - **tasks**: Task management and tracking (Includes DCI fields: `driver`, `contributors`, `informed`)
  - **teams**: Groups of users for collaboration

Each table includes appropriate indexes for efficient querying and search capabilities.

## Integration Points

The application integrates with several external services:

1. **Authentication Providers**: Via Auth.js
2. **OpenAI**: For AI-powered features (Gemini via Vercel SDK, Realtime API via WebRTC)
3. **Bill.com**: For financial data integration
4. **Convex**: For file storage
5. **Vercel Analytics**: For usage tracking

These integrations are managed through a combination of environment variables, API clients, and adapter patterns to ensure consistent interfaces.

## Technical Constraints

1. **Browser Compatibility**: Modern browsers only (Chrome, Firefox, Safari, Edge)
2. **Responsive Design**: Supports desktop and tablet, with limited mobile functionality
3. **Real-time Updates**: Relies on Convex's real-time capabilities
4. **API Rate Limits**: External services (Google AI, Trieve) have rate limits that must be respected
5. **File Size Limits**: File storage (Convex or potentially Trieve's S3) has limitations
6. **Authentication Flow**: Follows Auth.js patterns and limitations
7. **Trieve Dependency**: Chat functionality now relies on Trieve for context retrieval when filters are applied.

## Performance Considerations

1. **Query Optimization**: Using appropriate Convex indexes for efficient data retrieval
2. **Component Rendering**: Careful management of re-renders with React hooks
3. **Bundle Size**: Monitoring and optimizing JavaScript bundle size
4. **Image Optimization**: Using Next.js image optimization
5. **Caching Strategy**: Leveraging Convex caching for repeated queries
6. **Lazy Loading**: Implementing lazy loading for non-critical components
7. **Pagination**: Standardized backend queries use `pagination` arg with `numItems`/`cursor`. Frontend uses manual state management. *(Newly Added)*
