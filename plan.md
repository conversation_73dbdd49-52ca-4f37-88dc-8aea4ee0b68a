# Entity Extraction Agent Implementation Plan

## Brief Introduction

### Goals and Outcomes

The Entity Extraction Agent is designed to automatically discover and extract named entities (people and organizations) from document content within the FOJO Digital Chief of Staff App. This intelligent agent will:

1. **Analyze Document Content**: Read markdown content from documents and identify named entities with high confidence
2. **Extract Structured Data**: Convert unstructured text into structured entity information with roles, relationships, and contact details
3. **Present for Review**: Display extracted entities in a clean UI tool interface for user review and selection
4. **Create Entities**: Integrate seamlessly with existing mutations to create people, organizations, and relationships
5. **Avoid Duplicates**: Check for existing entities and update them with new information rather than creating duplicates

**Expected Outcomes:**
- Transform unstructured document text into actionable, structured data
- Reduce manual data entry by automatically identifying key people and organizations
- Maintain data quality through confidence scoring and user review
- Establish proper relationships between clients and discovered entities

## Instructions for AI Coding Agent

**IMPORTANT GUIDELINES:**
- [ ] Check off each item in this plan as it's completed
- [ ] Verify that each step works before moving to the next step
- [ ] If you get stuck or have questions, STOP and ask the user for further guidance
- [ ] Use `internalQuery` and `internalMutation` for functions that don't need client access
- [ ] Use `internalAction` for AI processing and external API calls
- [ ] Only use HTTP actions when you truly need a public HTTP endpoint
- [ ] Follow the existing patterns in the codebase for Zod schemas, mutations, and queries
- [ ] At the end, run `pnpm run lint` and `tsc --noemit` to ensure code quality

## High-Level Strategy

The implementation follows a clean separation of concerns:

1. **Backend Agent**: Handles AI processing and entity extraction using OpenAI
2. **Integration Layer**: Uses existing mutations for data persistence (no duplicate code)
3. **Frontend Tool**: Provides user interface for reviewing and confirming extractions
4. **Smart Processing**: Checks for duplicates and updates existing entities

## System Flow Diagram

```mermaid
flowchart TD
    A[User Views Document] --> B[Agent Reads Markdown Content]
    B --> C[AI Extracts Named Entities]
    C --> D[Structure Entity Data]
    D --> E[Present in Tool UI]
    E --> F{User Confirms?}
    F -->|Yes| G[Check for Existing Entities]
    F -->|No| H[End Process]
    G --> I[Create/Update People]
    I --> J[Create/Update Organizations]
    J --> K[Create Relationships]
    K --> L[Success Feedback]
    
    subgraph "AI Processing"
        C1[OpenAI GPT-4o]
        C2[Confidence Scoring]
        C3[Role Detection]
        C4[Contact Extraction]
    end
    
    C --> C1
    C1 --> C2
    C2 --> C3
    C3 --> C4
    C4 --> D
    
    subgraph "Existing Mutations"
        M1[createPeople]
        M2[updatePeople]
        M3[createOrganizations]
        M4[updateOrganizations]
        M5[createRelationship]
    end
    
    I --> M1
    I --> M2
    J --> M3
    J --> M4
    K --> M5
```

## Flow Breakdown

### Phase 1: Backend Infrastructure (Foundation)

#### 1.1 Create Agent Directory Structure
- [ ] Create `convex/agents/` directory
- [ ] Create `convex/agents/entityExtractionAgent.ts` file
- [ ] Add proper imports and "use node" directive

#### 1.2 Define Entity Extraction Schema
- [ ] Create Zod schema for person extraction with fields. colocate this in convex/agents/entityExtractionSchema.ts:
  - `name: string`
  - `email?: string`
  - `phone?: string`
  - `title?: string`
  - `description?: string`
  - `organizationName?: string` (If the person is part of an organization)
  - `relationshipType: string` (e.g., "Attorney", "Accountant", "Spouse")
  - `relationshipNotes?: string`
  - `confidence: "high" | "medium" | "low"`

- [ ] Create Zod schema for organization extraction with fields:
  - `name: string`
  - `email?: string`
  - `phone?: string`
  - `website?: string`
  - `description?: string`
  - `is_vendor: boolean`
  - `relationshipType: string`
  - `relationshipNotes?: string`
  - `confidence: "high" | "medium" | "low"`

- [ ] Create combined extraction result schema with:
  - `people: PersonExtraction[]`
  - `organizations: OrganizationExtraction[]`
  - `summary: string`

#### 1.3 Create Agent Instance
- [ ] Initialize OpenAI Agent with GPT-4o model
- [ ] Configure agent instructions for entity extraction
- [ ] Set up structured output generation

#### 1.4 Implement Core Extraction Action
- [ ] Create `extractEntitiesFromDocument` internal action with args:
  - `clientId: Id<"clients">`
  - `documentContent: string`
  - `fieldName: string`
  - `processImmediately?: boolean`

- [ ] Implement client context retrieval
- [ ] Create extraction prompt with client context
- [ ] Generate structured entities using agent
- [ ] Return extraction results

### Phase 2: Entity Processing Logic

#### 2.1 Create Helper Queries
- [ ] Create `getClientInfo` internal query
- [ ] Create `getRelationshipTypeByName` internal query with fallback logic
- [ ] Create `getClientDocuments` internal query for related documents

#### 2.2 Implement Person Processing
- [ ] Check for existing person by **email**, then by **name** using `getPersonByName`. Prioritize email for accuracy.
- [ ] If exists, update with new information using `updatePeople`.
- [ ] If not exists, create new person using `createPeople`.
- [ ] Handle contact information merging (don't overwrite existing data).

#### 2.3 Implement Organization Processing
- [ ] Check for existing organization using the `getOrganizationByName` query, which performs fuzzy matching.
- [ ] If exists, update with new information using `updateOrganizations`.
- [ ] If not exists, create new organization using `createOrganizations`.
- [ ] Handle vendor classification and contact information.

#### 2.4 Implement Relationship Creation
- [ ] **Query for Relationship Type**: Use the `getRelationshipTypeByName` query to find the `_id` of the relationship type based on the extracted `relationshipType` string.
- [ ] **Handle Missing Types**: If no matching relationship type is found, flag the entity for manual review. **Do not create new relationship types.**
- [ ] **Create Relationships**: Use the existing `createRelationship` mutation with the retrieved `relationship_type_id`.
- [ ] **Handle `organization_people`**: If an extracted person has an `organizationName`, use the `createOrganizationPeopleRelationship` mutation to link them with their role (e.g., "CEO").
- [ ] Handle relationship notes and context.

#### 2.5 Create Batch Processing Function
- [ ] Create `extractEntitiesFromClient` internal action
- [ ] Process multiple client fields (research, description, etc.)
- [ ] Process related documents
- [ ] Return comprehensive results

### Phase 3: Frontend Tool Interface

#### 3.1 Create Entity Review Component
- [ ] Create `components/agents/EntityExtractionTool.tsx`
- [ ] Design clean UI for displaying extracted entities
- [ ] Show confidence levels with visual indicators
- [ ] Group people and organizations separately

#### 3.2 Implement Selection Interface
- [ ] Add checkboxes for each extracted entity
- [ ] Show entity details (name, role, confidence)
- [ ] Allow bulk selection/deselection
- [ ] Display relationship context

#### 3.3 Create Confirmation Flow
- [ ] Add "Create Selected Entities" button
- [ ] Show processing state during creation
- [ ] Display success/error feedback
- [ ] Show created entity counts and any errors

#### 3.4 Integrate with Document Views
- [ ] Add "Extract Entities" button to document interfaces
- [ ] Trigger extraction for current document
- [ ] Show extraction tool in modal or sidebar
- [ ] Handle different document types (KB articles, meeting notes, etc.)

### Phase 4: Error Handling and Edge Cases

#### 4.1 Implement Robust Error Handling
- [ ] Handle OpenAI API failures gracefully
- [ ] Manage rate limiting and retries
- [ ] Log extraction errors for debugging
- [ ] Provide meaningful error messages to users

#### 4.2 Handle Duplicate Detection
- [ ] Implement fuzzy name matching for better duplicate detection
- [ ] Handle name variations (e.g., "John Smith" vs "J. Smith")
- [ ] Merge contact information intelligently
- [ ] Avoid creating duplicate relationships

#### 4.3 Manage Confidence Thresholds
- [ ] Filter out low-confidence extractions by default
- [ ] Allow users to adjust confidence thresholds
- [ ] Provide explanations for confidence scores
- [ ] Handle edge cases in entity recognition

### Phase 5: Integration and Testing

#### 5.1 Create Test Documents
- [ ] Create sample documents with known entities
- [ ] Test with various document types and formats
- [ ] Include edge cases (similar names, incomplete information)
- [ ] Verify extraction accuracy

#### 5.2 Test Entity Creation Flow
- [ ] Verify people are created correctly
- [ ] Verify organizations are created correctly
- [ ] Verify relationships are established properly
- [ ] Test duplicate handling

#### 5.3 Test UI Integration
- [ ] Test extraction tool interface
- [ ] Verify selection and confirmation flow
- [ ] Test error states and edge cases
- [ ] Ensure responsive design

### Phase 6: Performance and Optimization

#### 6.1 Optimize AI Prompts
- [ ] Refine extraction prompts for better accuracy
- [ ] Test with various document types
- [ ] Optimize for speed and token usage
- [ ] Handle large documents efficiently

#### 6.2 Implement Caching
- [ ] Cache extraction results to avoid re-processing
- [ ] Implement smart cache invalidation
- [ ] Store extraction metadata for debugging

#### 6.3 Add Monitoring
- [ ] Log extraction success/failure rates
- [ ] Monitor processing times
- [ ] Track user confirmation rates
- [ ] Identify common extraction patterns

### Phase 7: Advanced Features

#### 7.1 Batch Processing
- [ ] Allow processing multiple documents at once
- [ ] Show progress for batch operations
- [ ] Handle partial failures gracefully
- [ ] Provide batch results summary

#### 7.2 Smart Suggestions
- [ ] Suggest relationship types based on context
- [ ] Recommend entity merging for similar names
- [ ] Provide entity enrichment suggestions
- [ ] Learn from user corrections

#### 7.3 Export and Reporting
- [ ] Allow exporting extraction results
- [ ] Generate entity discovery reports
- [ ] Show extraction statistics
- [ ] Provide audit trail for created entities

### Phase 8: Final Testing and Deployment

#### 8.1 Comprehensive Testing
- [ ] Run full test suite
- [ ] Test with real client documents
- [ ] Verify performance under load
- [ ] Test error recovery scenarios

#### 8.2 Code Quality Checks
- [ ] Run `pnpm run lint` and fix any issues
- [ ] Run `tsc --noemit` and resolve TypeScript errors
- [ ] Review code for best practices
- [ ] Add JSDoc comments for public functions

#### 8.3 Documentation
- [ ] Document agent configuration
- [ ] Create user guide for extraction tool
- [ ] Document API endpoints and schemas
- [ ] Add troubleshooting guide

## Implementation Notes

### Key Patterns to Follow

1. **Use Existing Mutations**: The agent should call existing `createPeople`, `updatePeople`, `createOrganizations`, `updateOrganizations`, and `createRelationship` mutations directly. Do not create wrapper functions.

2. **Internal Functions**: Use `internalAction` for AI processing, `internalQuery` for data retrieval, and `internalMutation` only if absolutely necessary.

3. **Error Handling**: Implement comprehensive error handling with meaningful messages and graceful degradation.

4. **Zod Validation**: Use Zod schemas for all data validation and type safety.

5. **Confidence Scoring**: Always include confidence levels and allow users to filter by confidence.

### Code Examples

#### Agent Configuration
```typescript
const extractionAgent = new Agent(components.agent, {
  chat: openai.chat("gpt-4o"),
  instructions: `You are an entity extraction specialist. Your job is to:
1. Read documents and identify named entities (people and organizations).
2. Extract relevant details about each entity.
3. Determine their relationship to the client (e.g., "Attorney", "Spouse", "Accounting Firm"). This relationship type MUST match one of the existing types in the system.
4. Return structured data for processing.

Important guidelines:
- Only extract entities that are clearly identifiable.
- Determine relationship types based on context.
- Mark confidence level based on available information.
- For organizations, assume they are vendors unless indicated otherwise.`,
});
```

#### Extraction Schema (Corrected)
```typescript
const EntityExtractionSchema = z.object({
  people: z.array(z.object({
    name: z.string(),
    email: z.string().optional(),
    phone: z.string().optional(),
    title: z.string().optional(),
    description: z.string().optional(),
    organizationName: z.string().optional().describe("The organization this person belongs to, if any."),
    relationshipType: z.string().describe("e.g., Attorney, Accountant, Spouse, Child"),
    relationshipNotes: z.string().optional(),
    confidence: z.enum(["high", "medium", "low"])
  })),
  organizations: z.array(z.object({
    name: z.string(),
    email: z.string().optional(),
    phone: z.string().optional(),
    website: z.string().optional(),
    description: z.string().optional(),
    is_vendor: z.boolean().default(true),
    relationshipType: z.string().describe("e.g., Law Firm, Accounting Firm, Bank"),
    relationshipNotes: z.string().optional(),
    confidence: z.enum(["high", "medium", "low"])
  })),
  summary: z.string().describe("Brief summary of entities found")
});
```

## Success Criteria

The implementation will be considered successful when:

1. [ ] Agent can accurately extract entities from various document types
2. [ ] UI tool provides clear, intuitive entity review interface
3. [ ] Integration with existing mutations works seamlessly
4. [ ] Duplicate detection prevents data duplication
5. [ ] Error handling provides meaningful feedback
6. [ ] Performance is acceptable for typical document sizes
7. [ ] Code passes all linting and TypeScript checks
8. [ ] User can successfully create entities from extracted data

## Final Validation

Before considering the implementation complete:

- [ ] Run `pnpm run lint` - all issues resolved
- [ ] Run `tsc --noemit` - no TypeScript errors
- [ ] Test extraction with sample documents
- [ ] Verify entity creation in database
- [ ] Test UI tool functionality
- [ ] Confirm error handling works properly
- [ ] Validate performance with larger documents
- [ ] Review code for security and best practices

---

**Note**: This plan should be executed step-by-step, with each phase building upon the previous one. The AI coding agent should verify each step works before proceeding to ensure a robust, reliable implementation.
