"use client";

import { useParams } from 'next/navigation';
import SecondaryNavMenu from '@/components/blocks/secondary-nav-menu';

const baseMenuItems = [
  { name: 'Overview', href: 'overview' },
  { name: 'Timeline', href: 'timeline' },
  { name: 'Projects', href: 'projects' },
  { name: 'Workspace', href: 'workspace' },
  { name: 'Documents', href: 'documents' },
  { name: 'Relationships', href: 'relationships' },
  { name: 'Meetings', href: 'meetings' },
];

export function ClientSubmenu() {
  const params = useParams();
  const rawId = params.id;
  const clientId = Array.isArray(rawId) ? rawId[0] : rawId ?? "";

  const menuItems = baseMenuItems.map(item => ({
    label: item.name,
    href: `/clients/${clientId}/${item.href}`,
  }));

  return <SecondaryNavMenu items={menuItems} />;
}
