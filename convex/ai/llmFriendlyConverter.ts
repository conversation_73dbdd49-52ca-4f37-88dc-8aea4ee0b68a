"use node";

import { z } from "zod";
import { zCustomAction } from "convex-helpers/server/zod";
import { zid } from "convex-helpers/server/zod";
import { NoOp } from "convex-helpers/server/customFunctions";
import { internalAction } from "../_generated/server";
import { internal } from "../_generated/api";
import { generateText } from "ai";
import { google } from "@ai-sdk/google";

/**
 * A wrapper around Convex's internalAction function that uses Zod for validation.
 */
const zInternalAction = zCustomAction(internalAction, NoOp);

/**
 * Zod schema for document source - either from storage or URL
 * Using z.string() instead of zid() to avoid type recursion
 */
const DocumentSourceSchema = z.union([
  z.object({
    type: z.literal("storage"),
    storageId: z.string(),
  }),
  z.object({
    type: z.literal("url"),
    url: z.string().url("Must be a valid URL"),
  }),
]);

/**
 * Convert a document (PDF, image, etc.) to clean, well-structured Markdown using AI
 * 
 * @description
 * This internal action processes documents from either Convex storage or external URLs,
 * converts them to Markdown using Google's Gemini model, and saves the result
 * to the documents table.
 * 
 * @example
 * // Convert from storage
 * await ctx.runAction(internal.ai.llmFriendlyConverter, {
 *   documentId: documentId,
 *   source: { type: "storage", storageId: storageId }
 * });
 * 
 * // Convert from URL
 * await ctx.runAction(internal.ai.llmFriendlyConverter, {
 *   documentId: documentId,
 *   source: { type: "url", url: "https://example.com/document.pdf" }
 * });
 * 
 * @throws {Error} If storage object not found or URL fetch fails
 * @throws {ZodError} If input validation fails
 */
export const llmFriendlyConverter = zInternalAction({
  args: {
    documentId: zid("documents"),
    source: DocumentSourceSchema,
  },
  returns: z.null(),
  handler: async (ctx, args) => {
    const { documentId, source } = args;

    try {
      // 1. Get document buffer
      let documentBuffer: Buffer;
      if (source.type === "storage") {
        const blob = await ctx.storage.get(source.storageId);
        if (!blob) {
          throw new Error(`Storage object not found: ${source.storageId}`);
        }
        documentBuffer = Buffer.from(await blob.arrayBuffer());
      } else { // source.type === "url"
        const response = await fetch(source.url);
        if (!response.ok) {
          throw new Error(`Failed to fetch URL: ${source.url} (Status: ${response.status})`);
        }
        documentBuffer = Buffer.from(await response.arrayBuffer());
      }

      // 2. Convert document to Markdown using AI
      const { text } = await generateText({
        model: google("gemini-2.5-flash-preview-04-17"),
        messages: [
          {
            role: 'user',
            content: [
              { 
                type: 'text', 
                text: "Convert this document to clean, well-structured Markdown. Preserve the original structure, formatting, and content while making it readable and properly formatted for Markdown." 
              },
              { 
                type: 'image', 
                image: documentBuffer, 
                mimeType: 'application/pdf' 
              }
            ]
          }
        ]
      });

      // 3. Save the converted Markdown to the document
      await ctx.runMutation(internal.files.documentsMutations.saveMarkdown, {
        documentId,
        markdown: text,
      });

      return null;
    } catch (error) {
      // Enhanced error logging for better debugging
      console.error("Error in llmFriendlyConverter:", {
        documentId,
        sourceType: source.type,
        error: error instanceof Error ? error.message : String(error),
      });
      throw error;
    }
  },
});
