import { internalMutation } from "../_generated/server";
import { v } from "convex/values";

const validEntityTypes = v.union(
  v.literal("person"),
  v.literal("organization"),
  v.literal("client")
);

const validFieldTypes = v.union(
    v.literal("text"),
    v.literal("number"),
    v.literal("boolean"),
    v.literal("date"),
    v.literal("select"),
    v.literal("currency")
);

async function seedRelationshipTypeLogic(ctx: any, args: any) {
    const existing = await ctx.db
        .query("relationship_types")
        .filter((q: any) => q.eq(q.field("relationship_name"), args.name))
        .first();

    if (existing) {
        console.log(`Relationship type "${args.name}" already exists. Skipping.`);
        return;
    }

    await ctx.db.insert("relationship_types", {
        relationship_name: args.name,
        relationship_category: args.category,
        valid_combinations: args.validCombinations,
        is_client_family: args.isClientFamily,
        is_bidirectional: args.isBidirectional,
        implies_client_status: args.impliesClientStatus,
        is_principal_type: args.isPrincipalType,
        custom_fields_schema: args.customFieldsSchema,
        updated_at: Date.now(),
    });

    console.log(`Created relationship type: "${args.name}"`);
}

export const seedRelationshipType = internalMutation({
  args: {
    name: v.string(),
    category: v.string(),
    validCombinations: v.array(
      v.object({
        source_type: validEntityTypes,
        target_type: validEntityTypes,
      })
    ),
    isClientFamily: v.boolean(),
    isBidirectional: v.boolean(),
    impliesClientStatus: v.boolean(),
    isPrincipalType: v.boolean(),
    customFieldsSchema: v.array(
      v.object({
        field_key: v.string(),
        field_name: v.string(),
        field_type: validFieldTypes,
        is_required: v.boolean(),
        field_order: v.number(),
        description: v.optional(v.string()),
        options: v.optional(v.array(v.string())),
        default_value: v.optional(v.string()),
      })
    ),
  },
  handler: seedRelationshipTypeLogic,
});

export const seedEmployeeType = internalMutation({
    args: {},
    handler: async (ctx) => {
        await seedRelationshipTypeLogic(ctx, {
            name: "Employee",
            category: "professional",
            validCombinations: [{ source_type: 'person', target_type: 'organization' }],
            isClientFamily: false,
            isBidirectional: false,
            impliesClientStatus: false,
            isPrincipalType: false,
            customFieldsSchema: [
                {
                    field_key: "title",
                    field_name: "Title",
                    field_type: "text",
                    is_required: false,
                    field_order: 1,
                    description: undefined,
                    options: undefined,
                    default_value: undefined
                }
            ]
        });
    }
});
