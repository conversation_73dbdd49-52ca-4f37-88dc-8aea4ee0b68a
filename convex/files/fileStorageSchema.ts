/**
 * File Storage Schema Definitions using Zod with Convex Helpers
 * 
 * This file defines Zod schemas for file storage operations including:
 * 1. File document schemas for queries and mutations
 * 2. Integration and related entity schemas
 * 3. Attendee and sub-table data schemas
 * 4. Request/response schemas for file operations
 * 
 * All schemas use convex-helpers zod validation for better type safety
 * and consistent validation patterns across the file storage module.
 */

import { z } from 'zod';
import { zid } from 'convex-helpers/server/zod';

// ========================================
// Core File Schema
// ========================================

/**
 * Complete file document schema for return validation
 * Matches the files table structure in Convex schema
 */
export const FileSchema = z.object({
  _id: zid("files"),
  _creationTime: z.number(),
  title: z.string().optional(),
  fileName: z.string().optional(),
  description: z.string().optional(),
  short_description: z.string().optional(),
  docType: z.enum(['KNOWLEDGE_BASE', 'MEETING_NOTES', 'CONTRACT', 'DOCUMENT', 'BILL']),
  updated_at: z.number().optional(),
  box_file_id: z.string().optional(),
  ownerId: zid('users').optional(),
  parentEntityId: z.union([
    zid('tasks'),
    zid('projects'),
    zid('decisions'),
    zid('organizations'),
    zid('users'),
    zid('people'),
    zid('bills')
  ]).optional(),
  uploadStatus: z.enum(['processing', 'completed', 'failed']).optional(),
  error: z.string().optional(),
  parsedData: z.object({
    summary: z.string().optional(),
    analysis: z.string().optional(),
    entities: z.array(z.string()).optional(),
    maritalStatus: z.string().optional(),
    spouses: z.array(z.object({
      name: z.string().optional(),
      dateOfBirth: z.string().optional(),
    })).optional(),
    children: z.array(z.object({
      name: z.string().optional(),
      dateOfBirth: z.string().optional(),
    })).optional(),
    assets: z.array(z.object({
      name: z.string().optional(),
      value: z.number().optional(),
      type: z.string().optional(),
      description: z.string().optional(),
    })).optional(),
  }).optional(),
  fileFilename: z.string().optional(),
  fileStorageId: zid('_storage').optional(),
  fileSize: z.number().optional(),
  fileExtension: z.string().optional(),
  fileHash: z.string().optional(),
  trieveIndexingJobId: zid("_scheduled_functions").optional(),
  shortDescriptionJobId: zid("_scheduled_functions").optional(),
});

// ========================================
// Integration Schema
// ========================================

/**
 * Integration document schema
 * Used for Box and other service integrations
 */
export const IntegrationSchema = z.object({
  _id: zid("integrations"),
  _creationTime: z.number(),
  immutable_slug: z.string(),
  name: z.string().optional(),
  enabled: z.boolean().optional(),
  config: z.record(z.string(), z.any()).optional(),
});

// ========================================
// Attendee Details Schema
// ========================================

/**
 * Schema for attendee details in meeting notes
 * Supports both people and organizations
 */
export const AttendeeDetailsSchema = z.object({
  id: z.union([zid("people"), zid("organizations")]),
  type: z.enum(["person", "organization"]),
  name: z.string(),
  image: z.string().optional(),
  initials: z.string(),
  email: z.string().optional(),
  organizations: z.array(z.object({
    id: zid("organizations"),
    name: z.string(),
  })),
});

// ========================================
// Related Entity Schema
// ========================================

/**
 * Schema for entities related to files via file_relationships
 */
export const RelatedEntitySchema = z.object({
  _id: z.union([
    zid("tasks"),
    zid("projects"),
    zid("decisions"),
    zid("organizations"),
    zid("users"),
    zid("people"),
    zid("bills"),
    zid("clients")
  ]),
  type: z.string(),
  name: z.string(),
});

// ========================================
// Sub-table Data Schema
// ========================================

/**
 * Schema for sub-table data (knowledge base, meeting notes, etc.)
 */
export const SubTableDataSchema = z.object({
  category: z.string().nullable(),
  content: z.string().nullable(),
});

// ========================================
// File Upload and Metadata Schemas
// ========================================

/**
 * Schema for saving file metadata after upload
 */
export const SaveFileMetadataArgsSchema = z.object({
  name: z.string().min(1, "File name is required"),
  docType: z.enum(['KNOWLEDGE_BASE', 'MEETING_NOTES', 'CONTRACT', 'DOCUMENT', 'BILL']),
  size: z.number().min(0, "File size must be positive").optional(),
  description: z.string().optional(),
  ownerId: zid('users').optional(),
  parentEntityId: z.union([
    zid('tasks'),
    zid('projects'),
    zid('decisions'),
    zid('organizations'),
    zid('users'),
    zid('people'),
    zid('bills')
  ]).optional(),
  fileStorageId: zid('_storage').optional(),
  fileFilename: z.string().optional(),
  fileExtension: z.string().optional(),
  fileHash: z.string().optional(),
});

/**
 * Response schema for file operations
 */
export const FileResponseSchema = z.object({
  fileId: zid("files"),
});

// ========================================
// Query Arguments Schemas
// ========================================

/**
 * Arguments for getting attendee details
 */
export const GetAttendeeDetailsArgsSchema = z.object({
  attendeeIds: z.array(z.string()),
});

/**
 * Arguments for getting related entities
 */
export const GetRelatedEntitiesArgsSchema = z.object({
  fileId: zid('files'),
});

/**
 * Arguments for getting recently modified files
 */
export const GetRecentlyModifiedFilesArgsSchema = z.object({
  limit: z.number().optional().default(5),
});

/**
 * Arguments for getting file by storage ID
 */
export const GetFileByStorageIdArgsSchema = z.object({
  storageId: zid('_storage'),
});

/**
 * Arguments for getting file by Box ID
 */
export const GetFileByBoxIdArgsSchema = z.object({
  boxFileId: z.string(),
});

/**
 * Arguments for getting sub-table data
 */
export const GetSubTableDataArgsSchema = z.object({
  fileId: zid('files'),
  docType: z.string(),
});

/**
 * Arguments for getting file URL
 */
export const GetFileUrlArgsSchema = z.object({
  fileId: zid('_storage'),
});

/**
 * Arguments for getting file record basic
 */
export const GetFileRecordBasicArgsSchema = z.object({
  fileId: zid('files'),
});

/**
 * Arguments for getting file by ID (internal)
 */
export const GetFileArgsSchema = z.object({
  fileId: zid("files"),
});

// ========================================
// Type Exports
// ========================================

export type FileDocument = z.infer<typeof FileSchema>;
export type Integration = z.infer<typeof IntegrationSchema>;
export type AttendeeDetails = z.infer<typeof AttendeeDetailsSchema>;
export type RelatedEntity = z.infer<typeof RelatedEntitySchema>;
export type SubTableData = z.infer<typeof SubTableDataSchema>;
export type SaveFileMetadataArgs = z.infer<typeof SaveFileMetadataArgsSchema>;
export type FileResponse = z.infer<typeof FileResponseSchema>;
export type GetAttendeeDetailsArgs = z.infer<typeof GetAttendeeDetailsArgsSchema>;
export type GetRelatedEntitiesArgs = z.infer<typeof GetRelatedEntitiesArgsSchema>;
export type GetRecentlyModifiedFilesArgs = z.infer<typeof GetRecentlyModifiedFilesArgsSchema>;
export type GetFileByStorageIdArgs = z.infer<typeof GetFileByStorageIdArgsSchema>;
export type GetFileByBoxIdArgs = z.infer<typeof GetFileByBoxIdArgsSchema>;
export type GetSubTableDataArgs = z.infer<typeof GetSubTableDataArgsSchema>;
export type GetFileUrlArgs = z.infer<typeof GetFileUrlArgsSchema>;
export type GetFileRecordBasicArgs = z.infer<typeof GetFileRecordBasicArgsSchema>;
export type GetFileArgs = z.infer<typeof GetFileArgsSchema>; 