"use node";

import { z } from 'zod';
import { zAction } from '../functions';
import { api, internal } from '../_generated/api';
import { ConvexError } from 'convex/values';
import { ActionCtx } from '../_generated/server';
import { Id } from '../_generated/dataModel';

/**
 * Updates the AI-assisted notes content based on the transcript.
 *
 * This action:
 * 1. Takes a meeting note file ID
 * 2. Fetches the transcript content
 * 3. Calls the AI to generate notes based on the transcript
 * 4. Updates the content field in the meeting_notes document
 * 5. Clears the scheduling flags
 *
 * @example
 * const result = await convex.runAction(api.actions.meetingNoteActions.updateAiNotesFromTranscript, {
 *   fileId: "abc123"
 * });
 *
 * @throws {ConvexError} If the meeting note is not found or AI generation fails
 */
export const updateAiNotesFromTranscript = zAction({
  args: {
    fileId: z.string().min(1, "File ID is required"),
  },
  output: z.object({
    success: z.boolean(),
    message: z.string(),
    generatedNotes: z.string().optional(),
  }),
  handler: async (
    ctx: ActionCtx,
    args: { fileId: string }
  ): Promise<{
    success: boolean;
    message: string;
    generatedNotes?: string;
  }> => {
    console.log(`Generating AI notes from transcript for meeting note ${args.fileId}`);

    try {
      // 1. Get the file record to verify it exists
      const fileId = args.fileId as Id<"files">;
      const fileRecord = await ctx.runQuery(api.files.files.getFileRecordBasic, { fileId });

      if (!fileRecord) {
        throw new ConvexError({
          message: `File record ${args.fileId} not found`,
          code: "NOT_FOUND",
        });
      }

      // 2. Get the meeting note record with the latest transcript
      const meetingNote = await ctx.runQuery(api.files.files.getMeetingNote, { fileId });

      if (!meetingNote) {
        throw new ConvexError({
          message: `Meeting note for file ${args.fileId} not found`,
          code: "NOT_FOUND",
        });
      }

      // 3. Extract the transcript
      const transcript = meetingNote.mnTranscript || "";

      // Skip if transcript is empty
      if (!transcript) {
        console.log(`Transcript is empty for meeting note ${args.fileId}, skipping notes generation`);
        return {
          success: false,
          message: "No transcript available to generate notes",
        };
      }

      // Log transcript details for debugging
      console.log(`[TRANSCRIPT DEBUG] Processing transcript for file ${args.fileId}:`);
      console.log(`[TRANSCRIPT DEBUG] Length: ${transcript.length} characters`);
      console.log(`[TRANSCRIPT DEBUG] Word count: ${transcript.split(/\s+/).filter(Boolean).length} words`);
      console.log(`[TRANSCRIPT DEBUG] First 100 chars: ${transcript.substring(0, 100)}...`);
      console.log(`[TRANSCRIPT DEBUG] Last 100 chars: ...${transcript.substring(transcript.length - 100)}`);

      // If transcript is suspiciously short, log a warning
      if (transcript.length < 500) {
        console.log(`[TRANSCRIPT WARNING] Transcript for ${args.fileId} is unusually short (${transcript.length} chars). Possible incomplete data.`);
      }

      // 4. Generate the notes using AI
      const generatedNotes = await ctx.runAction(api.actions.aiUtils.generateDescriptionWithPrompt, {
        sourceText: transcript,
        promptSlug: "meeting-notes-from-transcript",
      });

      // 5. Update the content field in the meeting_notes document
      // Use the internal mutation to update the content field
      // This will find the meeting_notes record by fileId and update it

      // Update the content field in the meeting_notes table using the new internal mutation
      await ctx.runMutation(internal.files.files.internalUpdateMeetingNoteContent, {
        fileId,
        content: generatedNotes
        });

        // Step 6 (Clear flags) removed as it seems redundant with current trigger/debounce flow.

        return {
          success: true,
        message: `Successfully generated and stored AI notes for meeting note ${args.fileId}`,
        generatedNotes,
      };
    } catch (error: any) {
      console.error(`Error in updateAiNotesFromTranscript for ${args.fileId}:`, error);

      return {
        success: false,
        message: error instanceof Error ? error.message : "Unknown error occurred",
      };
    }
  },
});
