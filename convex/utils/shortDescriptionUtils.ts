import { MutationCtx } from "../_generated/server";
import { Id } from "../_generated/dataModel";
import { api } from "../_generated/api";
import { scheduleDebouncedJob } from "./schedulerUtils";

/**
 * Standard delay for debounced short description operations in milliseconds
 */
export const SHORT_DESCRIPTION_DEBOUNCE_DELAY = 10000; // 10 seconds

/**
 * Helper function to schedule field generation for a document with debouncing.
 * This is used by triggers to schedule the generation of a field value using AI.
 *
 * @param ctx - The mutation context
 * @param tableName - The name of the table containing the document
 * @param documentId - The ID of the document to update
 * @param sourceText - The source text to generate from
 * @param targetFieldName - The name of the field to update
 * @param promptSlug - The slug of the prompt to use
 * @param jobIdFieldName - The field name where the scheduled job ID will be stored
 * @param delayMs - Optional delay in milliseconds (defaults to SHORT_DESCRIPTION_DEBOUNCE_DELAY)
 */
export async function scheduleFieldGeneration<T extends string>(
  ctx: MutationCtx,
  tableName: T,
  documentId: Id<T>,
  sourceText: any,
  targetFieldName: string,
  promptSlug: string,
  jobIdFieldName: string,
  delayMs: number = SHORT_DESCRIPTION_DEBOUNCE_DELAY
): Promise<void> {
  // Ensure sourceText is a string
  const sourceTextStr = String(sourceText);

  console.log(`${tableName} source field changed for ${documentId}, scheduling ${targetFieldName} generation with prompt ${promptSlug} (debounced ${delayMs}ms)`);
 
  try {
    // Use the generic scheduleDebouncedJob function
    await scheduleDebouncedJob(ctx, {
      tableName,
      documentId,
      jobIdFieldName,
      jobToSchedule: api.actions.orchestrators.generateAndStoreField,
      jobArgs: {
        tableName,
        documentId: documentId.toString(), // Convert Id object to string
        sourceText: sourceTextStr,
        targetFieldName,
        promptSlug,
        jobIdFieldName, // Pass the field name so it can be cleared upon completion
      },
      delayMs,
    });

    console.log(`Debounced ${targetFieldName} generation scheduled for ${tableName}: ${documentId}`);
  } catch (error) {
    console.error(`Error scheduling ${targetFieldName} generation for ${tableName} ${documentId}:`, error);
  }
}
