import { mutation } from "../_generated/server";
import { v } from "convex/values";
import { ConvexError } from "convex/values";
import { Id } from "../_generated/dataModel";
import {
  RelationshipTypeInputSchema,
  RelationshipTypeUpdateSchema,
  RelationshipInputSchema,
  RelationshipUpdateSchema,
  RelationshipTypeSchema,
  RelationshipSchema,
  EntityTypeSchema,
  isValidCombination,
  validateCustomFields
} from "./relationshipSchema";

// ========================================
// RELATIONSHIP TYPE MUTATIONS
// ========================================

/**
 * Create a new relationship type
 */
export const createRelationshipType = mutation({
  args: {
    input: v.any() // Will be validated with <PERSON><PERSON>
  },
  returns: v.id("relationship_types"),
  handler: async (ctx, args) => {
    // Validate input with Zod
    const validatedInput = RelationshipTypeInputSchema.parse(args.input);

    // Check for existing relationship type with same name
    const existing = await ctx.db
      .query("relationship_types")
      .filter(q => q.eq(q.field("relationship_name"), validatedInput.relationship_name))
      .first();

    if (existing) {
      throw new ConvexError(`Relationship type with name "${validatedInput.relationship_name}" already exists`);
    }

    // Validate custom fields schema if provided
    if (validatedInput.custom_fields_schema) {
      for (const field of validatedInput.custom_fields_schema) {
        if (field.field_type === 'select' && (!field.options || field.options.length === 0)) {
          throw new ConvexError(`Select field "${field.field_name}" must have options defined`);
        }
      }
    }

    // Create the relationship type
    const relationshipTypeId = await ctx.db.insert("relationship_types", {
      ...validatedInput,
      updated_at: Date.now()
    });

    return relationshipTypeId;
  },
});

/**
 * Update an existing relationship type
 */
export const updateRelationshipType = mutation({
  args: {
    relationshipTypeId: v.id("relationship_types"),
    updates: v.any() // Will be validated with Zod
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    // Validate updates with Zod
    const validatedUpdates = RelationshipTypeUpdateSchema.parse(args.updates);

    // Check if relationship type exists
    const existing = await ctx.db.get(args.relationshipTypeId);
    if (!existing) {
      throw new ConvexError("Relationship type not found");
    }

    // If updating name, check for conflicts
    if (validatedUpdates.relationship_name) {
      const nameConflict = await ctx.db
        .query("relationship_types")
        .filter(q => 
          q.and(
            q.eq(q.field("relationship_name"), validatedUpdates.relationship_name),
            q.neq(q.field("_id"), args.relationshipTypeId)
          )
        )
        .first();

      if (nameConflict) {
        throw new ConvexError(`Relationship type with name "${validatedUpdates.relationship_name}" already exists`);
      }
    }

    // Validate custom fields schema if provided
    if (validatedUpdates.custom_fields_schema) {
      for (const field of validatedUpdates.custom_fields_schema) {
        if (field.field_type === 'select' && (!field.options || field.options.length === 0)) {
          throw new ConvexError(`Select field "${field.field_name}" must have options defined`);
        }
      }
    }

    // Update the relationship type
    await ctx.db.patch(args.relationshipTypeId, {
      ...validatedUpdates,
      updated_at: Date.now()
    });

    return null;
  },
});

/**
 * Delete a relationship type (only if no relationships use it)
 */
export const deleteRelationshipType = mutation({
  args: {
    relationshipTypeId: v.id("relationship_types")
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    // Check if relationship type exists
    const relationshipType = await ctx.db.get(args.relationshipTypeId);
    if (!relationshipType) {
      throw new ConvexError("Relationship type not found");
    }

    // Check if any relationships use this type
    const existingRelationships = await ctx.db
      .query("relationships")
      .withIndex("by_type", q => q.eq("relationship_type_id", args.relationshipTypeId))
      .first();

    if (existingRelationships) {
      throw new ConvexError("Cannot delete relationship type that is being used by existing relationships");
    }

    // Delete the relationship type
    await ctx.db.delete(args.relationshipTypeId);

    return null;
  },
});

// ========================================
// RELATIONSHIP MUTATIONS
// ========================================

/**
 * Create a new relationship (compatibility export)
 */
export const createRelationship = mutation({
  args: {
    source_type: v.string(),
    source_id: v.string(),
    target_type: v.string(), 
    target_id: v.string(),
    relationship_type_id: v.id("relationship_types"),
    is_active: v.optional(v.boolean()),
    relationship_start: v.optional(v.number()),
    relationship_end: v.optional(v.number()),
    relationship_notes: v.optional(v.string()),
    custom_fields: v.optional(v.any()),
    authority_limit: v.optional(v.number())
  },
  returns: v.id("relationships"),
  handler: async (ctx, args) => {
    // Validate input with Zod
    const validatedInput = RelationshipInputSchema.parse(args);

    // Validate relationship type exists
    const relationshipType = await ctx.db.get(validatedInput.relationship_type_id);
    if (!relationshipType) {
      throw new ConvexError("Relationship type not found");
    }

    // Validate entity combination is allowed for this relationship type
    if (!isValidCombination(relationshipType as any, validatedInput.source_type, validatedInput.target_type)) {
      throw new ConvexError(
        `Invalid entity combination: ${validatedInput.source_type} -> ${validatedInput.target_type} ` +
        `is not allowed for relationship type "${relationshipType.relationship_name}"`
      );
    }

    // Validate that source and target entities exist
    await validateEntityExists(ctx, validatedInput.source_type, validatedInput.source_id);
    await validateEntityExists(ctx, validatedInput.target_type, validatedInput.target_id);

    // Validate custom fields if provided
    if (validatedInput.custom_fields && relationshipType.custom_fields_schema) {
      const validation = validateCustomFields(validatedInput.custom_fields, relationshipType.custom_fields_schema);
      if (!validation.success) {
        throw new ConvexError(`Custom fields validation failed: ${validation.error.message}`);
      }
    }

    // Check for existing active relationship between these entities
    const existingRelationship = await ctx.db
      .query("relationships")
      .filter(q => 
        q.and(
          q.eq(q.field("source_type"), validatedInput.source_type),
          q.eq(q.field("source_id"), validatedInput.source_id),
          q.eq(q.field("target_type"), validatedInput.target_type),
          q.eq(q.field("target_id"), validatedInput.target_id),
          q.eq(q.field("relationship_type_id"), validatedInput.relationship_type_id),
          q.eq(q.field("is_active"), true)
        )
      )
      .first();

    if (existingRelationship) {
      throw new ConvexError("An active relationship of this type already exists between these entities");
    }

    // Create the relationship
    const relationshipId = await ctx.db.insert("relationships", {
      ...validatedInput,
      is_active: validatedInput.is_active ?? true,
      updated_at: Date.now()
    });

    // Handle bidirectional relationships
    if (relationshipType.is_bidirectional) {
      await ctx.db.insert("relationships", {
        source_type: validatedInput.target_type,
        source_id: validatedInput.target_id,
        target_type: validatedInput.source_type,
        target_id: validatedInput.source_id,
        relationship_type_id: validatedInput.relationship_type_id,
        is_active: validatedInput.is_active ?? true,
        relationship_start: validatedInput.relationship_start,
        relationship_end: validatedInput.relationship_end,
        relationship_notes: validatedInput.relationship_notes,
        custom_fields: validatedInput.custom_fields,
        authority_limit: validatedInput.authority_limit,
        updated_at: Date.now()
      });
    }

    return relationshipId;
  },
});

/**
 * Update an existing relationship
 */
export const updateRelationship = mutation({
  args: {
    relationshipId: v.id("relationships"),
    updates: v.any() // Will be validated with Zod
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    // Validate updates with Zod
    const validatedUpdates = RelationshipUpdateSchema.parse(args.updates);

    // Check if relationship exists
    const existing = await ctx.db.get(args.relationshipId);
    if (!existing) {
      throw new ConvexError("Relationship not found");
    }

    // Get relationship type for validation if custom fields are being updated
    if (validatedUpdates.custom_fields) {
      const relationshipType = await ctx.db.get(existing.relationship_type_id);
      if (!relationshipType) {
        throw new ConvexError("Relationship type not found");
      }

      // Validate custom fields if provided
      if (relationshipType.custom_fields_schema) {
        const validation = validateCustomFields(validatedUpdates.custom_fields, relationshipType.custom_fields_schema);
        if (!validation.success) {
          throw new ConvexError(`Custom fields validation failed: ${validation.error.message}`);
        }
      }
    }

    // Update the relationship
    await ctx.db.patch(args.relationshipId, {
      ...validatedUpdates,
      updated_at: Date.now()
    });

    return null;
  },
});

/**
 * Delete a relationship
 */
export const deleteRelationship = mutation({
  args: {
    relationshipId: v.id("relationships")
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    // Check if relationship exists
    const relationship = await ctx.db.get(args.relationshipId);
    if (!relationship) {
      throw new ConvexError("Relationship not found");
    }

    // Delete the relationship
    await ctx.db.delete(args.relationshipId);

    return null;
  },
});

/**
 * Deactivate a relationship (soft delete)
 */
export const deactivateRelationship = mutation({
  args: {
    relationshipId: v.id("relationships")
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    // Check if relationship exists
    const relationship = await ctx.db.get(args.relationshipId);
    if (!relationship) {
      throw new ConvexError("Relationship not found");
    }

    // Deactivate the relationship
    await ctx.db.patch(args.relationshipId, {
      is_active: false,
      relationship_end: Date.now(),
      updated_at: Date.now()
    });

    return null;
  },
});

/**
 * Reactivate a relationship
 */
export const reactivateRelationship = mutation({
  args: {
    relationshipId: v.id("relationships")
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    // Check if relationship exists
    const relationship = await ctx.db.get(args.relationshipId);
    if (!relationship) {
      throw new ConvexError("Relationship not found");
    }

    // Reactivate the relationship
    await ctx.db.patch(args.relationshipId, {
      is_active: true,
      relationship_end: undefined,
      updated_at: Date.now()
    });

    return null;
  },
});

/**
 * Delete all relationships for a specific entity
 */
export const deleteEntityRelationships = mutation({
  args: {
    entityType: v.string(),
    entityId: v.string()
  },
  returns: v.number(),
  handler: async (ctx, args) => {
    // Validate entity type
    const entityType = EntityTypeSchema.parse(args.entityType);

    // Get all relationships where this entity is involved
    const sourceRelationships = await ctx.db
      .query("relationships")
      .filter(q => 
        q.and(
          q.eq(q.field("source_type"), entityType),
          q.eq(q.field("source_id"), args.entityId)
        )
      )
      .collect();

    const targetRelationships = await ctx.db
      .query("relationships")
      .filter(q => 
        q.and(
          q.eq(q.field("target_type"), entityType),
          q.eq(q.field("target_id"), args.entityId)
        )
      )
      .collect();

    const allRelationships = [...sourceRelationships, ...targetRelationships];

    // Delete all relationships
    await Promise.all(
      allRelationships.map(relationship => ctx.db.delete(relationship._id))
    );

    return allRelationships.length;
  },
});

// ========================================
// HELPER FUNCTIONS
// ========================================

/**
 * Validate that an entity exists
 */
async function validateEntityExists(ctx: any, entityType: string, entityId: any) {
  let entity;
  
  switch (entityType) {
    case 'person':
      entity = await ctx.db.get(entityId as Id<"people">);
      break;
    case 'organization':
      entity = await ctx.db.get(entityId as Id<"organizations">);
      break;
    case 'client':
      entity = await ctx.db.get(entityId as Id<"clients">);
      break;
    default:
      throw new ConvexError(`Invalid entity type: ${entityType}`);
  }

  if (!entity) {
    throw new ConvexError(`${entityType} with ID ${entityId} not found`);
  }

  return entity;
}
