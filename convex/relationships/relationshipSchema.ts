import { z } from 'zod';
import { zid } from 'convex-helpers/server/zod';
import { Doc, Id } from '../_generated/dataModel';

// ========================================
// SHARED ENUMS AND TYPES
// ========================================

// Entity types that can participate in relationships
export const EntityTypeSchema = z.enum(['person', 'organization', 'client']);
export type EntityType = z.infer<typeof EntityTypeSchema>;

// Relationship categories for grouping
export const RelationshipCategorySchema = z.enum(['family', 'professional', 'business']);
export type RelationshipCategory = z.infer<typeof RelationshipCategorySchema>;

// Field types for custom fields
export const CustomFieldTypeSchema = z.enum([
  'text',
  'number',
  'date',
  'boolean',
  'select',
  'currency'
]);
export type CustomFieldType = z.infer<typeof CustomFieldTypeSchema>;

// Valid entity combinations for relationships
export const ValidCombinationSchema = z.object({
  source_type: EntityTypeSchema,
  target_type: EntityTypeSchema
});
export type ValidCombination = z.infer<typeof ValidCombinationSchema>;

// Union type for polymorphic IDs based on entity type
export const EntityIdSchema = z.union([
  zid('people'),
  zid('organizations'),
  zid('clients')
]);
export type EntityId = z.infer<typeof EntityIdSchema>;

// ========================================
// CUSTOM FIELD SCHEMA
// ========================================

// Schema for defining custom fields on relationship types
export const CustomFieldDefinitionSchema = z.object({
  field_key: z.string().min(1).regex(/^[a-z_][a-z0-9_]*$/, 'Field key must be lowercase with underscores'),
  field_name: z.string().min(1),
  field_type: CustomFieldTypeSchema,
  is_required: z.boolean().optional(),
  options: z.array(z.string()).optional(), // For select fields
  description: z.string().optional(),
  default_value: z.string().optional(),
  field_order: z.number()
});
export type CustomFieldDefinition = z.infer<typeof CustomFieldDefinitionSchema>;

// ========================================
// RELATIONSHIP TYPE SCHEMAS
// ========================================

// Base relationship type schema
export const BaseRelationshipTypeSchema = z.object({
  relationship_name: z.string().min(1),
  relationship_description: z.string().optional(),
  relationship_category: RelationshipCategorySchema.optional(),
  
  // Valid entity combinations
  valid_combinations: z.array(ValidCombinationSchema).min(1),
  
  // Behavior flags
  is_client_family: z.boolean(),
  is_bidirectional: z.boolean(),
  implies_client_status: z.boolean(),
  is_principal_type: z.boolean(),
  
  // Custom fields schema
  custom_fields_schema: z.array(CustomFieldDefinitionSchema).optional(),
  
  updated_at: z.number()
});

// Input schema for creating a relationship type
export const RelationshipTypeInputSchema = BaseRelationshipTypeSchema.omit({ updated_at: true });
export type RelationshipTypeInput = z.infer<typeof RelationshipTypeInputSchema>;

// Complete relationship type schema with system fields
export const RelationshipTypeSchema = BaseRelationshipTypeSchema.extend({
  _id: zid('relationship_types'),
  _creationTime: z.number()
});
export type RelationshipType = z.infer<typeof RelationshipTypeSchema>;

// Update schema for relationship types
export const RelationshipTypeUpdateSchema = BaseRelationshipTypeSchema.partial();
export type RelationshipTypeUpdate = z.infer<typeof RelationshipTypeUpdateSchema>;

// ========================================
// RELATIONSHIP SCHEMAS
// ========================================

// Base relationship schema
export const BaseRelationshipSchema = z.object({
  // Source entity
  source_type: EntityTypeSchema,
  source_id: EntityIdSchema,
  
  // Target entity
  target_type: EntityTypeSchema,
  target_id: EntityIdSchema,
  
  // Relationship type reference
  relationship_type_id: zid('relationship_types'),
  
  // Core relationship data
  relationship_start: z.number().optional(),
  relationship_end: z.number().optional(),
  is_active: z.boolean(),
  relationship_notes: z.string().optional(),
  
  // Authority limit for financial relationships
  authority_limit: z.number().min(0).optional(),
  
  // Custom fields stored as flexible object
  custom_fields: z.any().optional(), // Will be validated against relationship type's schema
  
  updated_at: z.number()
});

// Input schema for creating a relationship
export const RelationshipInputSchema = BaseRelationshipSchema.omit({ updated_at: true });
export type RelationshipInput = z.infer<typeof RelationshipInputSchema>;

// Complete relationship schema with system fields
export const RelationshipSchema = BaseRelationshipSchema.extend({
  _id: zid('relationships'),
  _creationTime: z.number()
});
export type Relationship = z.infer<typeof RelationshipSchema>;

// Update schema for relationships
export const RelationshipUpdateSchema = BaseRelationshipSchema
  .omit({ 
    source_type: true, 
    source_id: true, 
    target_type: true, 
    target_id: true,
    relationship_type_id: true 
  })
  .partial();
export type RelationshipUpdate = z.infer<typeof RelationshipUpdateSchema>;

// ========================================
// QUERY AND FILTER SCHEMAS
// ========================================

// Relationship type filter schema
export const RelationshipTypeFilterSchema = z.object({
  relationship_category: RelationshipCategorySchema.optional(),
  is_client_family: z.boolean().optional(),
  is_bidirectional: z.boolean().optional(),
  is_principal_type: z.boolean().optional(),
  source_type: EntityTypeSchema.optional(),
  target_type: EntityTypeSchema.optional(),
  search_text: z.string().optional()
});
export type RelationshipTypeFilter = z.infer<typeof RelationshipTypeFilterSchema>;

// Relationship filter schema
export const RelationshipFilterSchema = z.object({
  // Entity filters
  source_type: EntityTypeSchema.optional(),
  source_id: EntityIdSchema.optional(),
  target_type: EntityTypeSchema.optional(),
  target_id: EntityIdSchema.optional(),
  
  // Either source or target (for finding all relationships of an entity)
  entity_type: EntityTypeSchema.optional(),
  entity_id: EntityIdSchema.optional(),
  
  // Relationship type filters
  relationship_type_id: zid('relationship_types').optional(),
  relationship_category: RelationshipCategorySchema.optional(),
  
  // Status filters
  is_active: z.boolean().optional(),
  
  // Date range filters
  relationship_start_from: z.number().optional(),
  relationship_start_to: z.number().optional(),
  relationship_end_from: z.number().optional(),
  relationship_end_to: z.number().optional(),
  
  // Authority filters
  min_authority_limit: z.number().optional(),
  max_authority_limit: z.number().optional()
});
export type RelationshipFilter = z.infer<typeof RelationshipFilterSchema>;

// Pagination schema (reused from clients-schema)
export const PaginationSchema = z.object({
  limit: z.number().min(1).max(100).default(50),
  cursor: z.string().optional(),
  sortBy: z.string().optional(),
  sortDirection: z.enum(['asc', 'desc']).default('desc')
});
export type Pagination = z.infer<typeof PaginationSchema>;

// ========================================
// RESPONSE SCHEMAS
// ========================================

// Enriched relationship with type details
export const RelationshipWithTypeSchema = RelationshipSchema.extend({
  relationship_type: RelationshipTypeSchema
});
export type RelationshipWithType = z.infer<typeof RelationshipWithTypeSchema>;

// Relationship with resolved entities
export const RelationshipWithEntitiesSchema = RelationshipSchema.extend({
  source_entity: z.object({
    _id: z.string(),
    name: z.string(),
    type: EntityTypeSchema,
    description: z.string().optional()
  }).nullable(),
  target_entity: z.object({
    _id: z.string(),
    name: z.string(),
    type: EntityTypeSchema,
    description: z.string().optional()
  }).nullable(),
  relationship_type: RelationshipTypeSchema.nullable()
});
export type RelationshipWithEntities = z.infer<typeof RelationshipWithEntitiesSchema>;

// Paginated response
export const PaginatedRelationshipResponseSchema = z.object({
  page: z.array(RelationshipWithEntitiesSchema),
  isDone: z.boolean(),
  continueCursor: z.string().nullable(),
});
export type PaginatedRelationshipResponse = z.infer<typeof PaginatedRelationshipResponseSchema>;

// ========================================
// BULK OPERATION SCHEMAS
// ========================================

// Bulk relationship creation
export const BulkRelationshipCreateSchema = z.object({
  relationships: z.array(RelationshipInputSchema)
});
export type BulkRelationshipCreate = z.infer<typeof BulkRelationshipCreateSchema>;

// Bulk relationship update
export const BulkRelationshipUpdateSchema = z.object({
  updates: z.array(z.object({
    relationship_id: zid('relationships'),
    updates: RelationshipUpdateSchema
  }))
});
export type BulkRelationshipUpdate = z.infer<typeof BulkRelationshipUpdateSchema>;

// ========================================
// NETWORK ANALYSIS SCHEMAS
// ========================================

// Entity network node
export const NetworkNodeSchema = z.object({
  id: z.string(),
  type: EntityTypeSchema,
  name: z.string(),
  description: z.string().optional(),
  tier: z.string().optional(), // For clients
  category: z.string().optional() // For grouping
});
export type NetworkNode = z.infer<typeof NetworkNodeSchema>;

// Entity network edge (relationship)
export const NetworkEdgeSchema = z.object({
  id: z.string(),
  source: z.string(),
  target: z.string(),
  relationship_type: z.string(),
  is_bidirectional: z.boolean(),
  strength: z.number().optional() // For visualization
});
export type NetworkEdge = z.infer<typeof NetworkEdgeSchema>;

// Complete network graph
export const NetworkGraphSchema = z.object({
  nodes: z.array(NetworkNodeSchema),
  edges: z.array(NetworkEdgeSchema)
});
export type NetworkGraph = z.infer<typeof NetworkGraphSchema>;

// ========================================
// VALIDATION HELPERS
// ========================================

// Validate custom fields against a relationship type's schema
export const validateCustomFields = (
  fields: any,
  schema: CustomFieldDefinition[]
): z.SafeParseReturnType<any, any> => {
  const fieldSchema = z.object(
    schema.reduce((acc, field) => {
      let validator: z.ZodSchema;
      
      switch (field.field_type) {
        case 'text':
          validator = z.string();
          break;
        case 'number':
        case 'currency':
          validator = z.number();
          break;
        case 'date':
          validator = z.number(); // Timestamp
          break;
        case 'boolean':
          validator = z.boolean();
          break;
        case 'select':
          validator = field.options ? z.enum(field.options as [string, ...string[]]) : z.string();
          break;
        default:
          validator = z.any();
      }
      
      if (!field.is_required) {
        validator = validator.optional();
      }
      
      acc[field.field_key] = validator;
      return acc;
    }, {} as Record<string, z.ZodSchema>)
  );
  
  return fieldSchema.safeParse(fields);
};

// Check if a relationship type supports a given entity combination
export const isValidCombination = (
  relationshipType: RelationshipType,
  sourceType: EntityType,
  targetType: EntityType
): boolean => {
  return relationshipType.valid_combinations.some(
    combo => combo.source_type === sourceType && combo.target_type === targetType
  );
};

// ========================================
// PREDEFINED RELATIONSHIP TYPES
// ========================================

// Common family relationship names
export const FAMILY_RELATIONSHIPS = {
  SPOUSE: 'Spouse',
  CHILD: 'Child',
  PARENT: 'Parent',
  SIBLING: 'Sibling',
  GRANDPARENT: 'Grandparent',
  GRANDCHILD: 'Grandchild',
  AUNT_UNCLE: 'Aunt/Uncle',
  NIECE_NEPHEW: 'Niece/Nephew',
  COUSIN: 'Cousin',
  IN_LAW: 'In-Law'
} as const;

// Common professional relationship names
export const PROFESSIONAL_RELATIONSHIPS = {
  ATTORNEY: 'Attorney',
  ACCOUNTANT: 'Accountant',
  FINANCIAL_ADVISOR: 'Financial Advisor',
  INSURANCE_AGENT: 'Insurance Agent',
  BANKER: 'Banker',
  INVESTMENT_MANAGER: 'Investment Manager',
  TAX_PREPARER: 'Tax Preparer',
  ESTATE_PLANNER: 'Estate Planner',
  TRUSTEE: 'Trustee',
  EXECUTOR: 'Executor'
} as const;

// Authority relationship names
export const AUTHORITY_RELATIONSHIPS = {
  POWER_OF_ATTORNEY: 'Power of Attorney',
  HEALTHCARE_PROXY: 'Healthcare Proxy',
  GUARDIAN: 'Guardian',
  CONSERVATOR: 'Conservator',
  AUTHORIZED_AGENT: 'Authorized Agent',
  FINANCIAL_REPRESENTATIVE: 'Financial Representative'
} as const;

// ========================================
// EXPORT COLLECTIONS
// ========================================

// Export all relationship-related schemas
export const RelationshipSchemas = {
  // Relationship type schemas
  RelationshipType: RelationshipTypeSchema,
  RelationshipTypeInput: RelationshipTypeInputSchema,
  RelationshipTypeUpdate: RelationshipTypeUpdateSchema,
  
  // Relationship schemas
  Relationship: RelationshipSchema,
  RelationshipInput: RelationshipInputSchema,
  RelationshipUpdate: RelationshipUpdateSchema,
  
  // Filter schemas
  RelationshipTypeFilter: RelationshipTypeFilterSchema,
  RelationshipFilter: RelationshipFilterSchema,
  
  // Response schemas
  RelationshipWithType: RelationshipWithTypeSchema,
  RelationshipWithEntities: RelationshipWithEntitiesSchema,
  PaginatedRelationshipResponse: PaginatedRelationshipResponseSchema,
  
  // Bulk operation schemas
  BulkRelationshipCreate: BulkRelationshipCreateSchema,
  BulkRelationshipUpdate: BulkRelationshipUpdateSchema,
  
  // Network analysis schemas
  NetworkNode: NetworkNodeSchema,
  NetworkEdge: NetworkEdgeSchema,
  NetworkGraph: NetworkGraphSchema,
  
  // Custom field schemas
  CustomFieldDefinition: CustomFieldDefinitionSchema,
  ValidCombination: ValidCombinationSchema
} as const;

// Export all types
export type RelationshipSchemaTypes = {
  [K in keyof typeof RelationshipSchemas]: z.infer<typeof RelationshipSchemas[K]>
};

// ========================================
// FRONTEND-SPECIFIC TYPES
// ========================================

// The shape of the data returned by the listRelationshipTypesForClient query
export type RelationshipTypeForClient = Doc<"relationship_types"> | null;

// Props for the main modal component
export interface AddRelationshipModalProps {
  clientId: Id<"clients">;
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void; // Optional callback when relationship is created successfully
}

// Form state for the modal
export interface RelationshipFormState {
  selectedTypeId: Id<"relationship_types"> | null;
  selectedTargetType: "person" | "organization" | "client" | null;
  selectedTargetId: Id<"people"> | Id<"organizations"> | Id<"clients"> | null;
  isActive: boolean;
  startDate: number | undefined;
  notes: string;
  customFields: Record<string, any>;
}

// Entity search result schema and type
export const EntitySearchResultSchema = z.object({
  _id: z.union([zid('people'), zid('organizations'), zid('clients')]),
  name: z.string(),
  type: EntityTypeSchema,
  description: z.string().optional(),
  imageUrl: z.string().nullable().optional(),
});
export type EntitySearchResult = z.infer<typeof EntitySearchResultSchema>;

// Step definitions for the modal workflow
export type ModalStep = "selectType" | "selectTarget" | "addDetails";

// Target type options based on relationship type
export interface TargetTypeOption {
  value: "person" | "organization" | "client";
  label: string;
  description: string;
}
