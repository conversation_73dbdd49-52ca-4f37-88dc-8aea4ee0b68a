import { zQuery } from '../functions';
import { paginationOptsValidator } from 'convex/server';
import { z } from 'zod';
import { zid } from 'convex-helpers/server/zod';
import { getManyFrom, getAll } from 'convex-helpers/server/relationships';
import { asyncMap } from 'convex-helpers';
import {
  ClientQuerySchema,
  ClientFilterSchema,
  PaginationSchema,
  ClientSchemas,
  ClientAssignmentFilterSchema,
  ClientStatusSchema,
  ClientTierSchema,
  ClientTypeSchema,
  ClientDetailsPageDataSchema,
} from '../../zod/clients-schema';
import { RelationshipSchemas } from '../relationships/relationshipSchema';
import { Task } from '../../zod/tasks-schema';

// Get single client by ID
export const getClient = zQuery({
  args: {
    id: zid('clients'),
  },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.id);
  },
});

// Get client with assignments and user details
export const getClientWithAssignments = zQuery({
  args: {
    id: zid('clients'),
  },
  handler: async (ctx, args) => {
    const client = await ctx.db.get(args.id);
    if (!client) return null;

    // Get all assignments for this client using relationship helper
    const assignments = await ctx.db
      .query('client_assignments')
      .withIndex('by_client_id', (q) => q.eq('client_id', args.id))
      .collect();

    // Get user details for each assignment using asyncMap
    const assignmentsWithUsers = await asyncMap(assignments, async (assignment) => {
      const user = await ctx.db.get(assignment.user_id);
      return {
        assignment,
        user: user ? {
          _id: user._id,
          name: user.name,
          email: user.email,
        } : null,
      };
    });

    return {
      ...client,
      assignments: assignmentsWithUsers.filter(a => a.user !== null),
    };
  },
});

// List clients with filtering and pagination
export const listClients = zQuery({
  args: ClientQuerySchema,
  handler: async (ctx, args) => {
    const { filter, pagination } = args;
    
    // Handle pagination options
    const paginationOpts = {
      numItems: pagination?.limit || 50,
      cursor: pagination?.cursor || null,
    };

    // Build filter function for additional filters
    const buildFilter = (q: any) => {
      if (!filter) return undefined;
      
      let conditions = q.eq(true, true); // Start with true

      // Date range filters
      if (filter.client_since_start) {
        conditions = q.and(
          conditions,
          q.gte(q.field('client_since'), filter.client_since_start)
        );
      }
      if (filter.client_since_end) {
        conditions = q.and(
          conditions,
          q.lte(q.field('client_since'), filter.client_since_end)
        );
      }

      // Net worth range
      if (filter.min_net_worth) {
        conditions = q.and(
          conditions,
          q.gte(q.field('client_net_worth'), filter.min_net_worth)
        );
      }
      if (filter.max_net_worth) {
        conditions = q.and(
          conditions,
          q.lte(q.field('client_net_worth'), filter.max_net_worth)
        );
      }

      // Search text using Convex text search approach
      if (filter.search_text) {
        const searchText = filter.search_text;
        conditions = q.and(
          conditions,
          q.or(
            q.eq(q.field('client_name'), searchText),
            q.neq(q.field('client_description'), undefined),
            q.neq(q.field('client_research'), undefined)
          )
        );
      }

      // Days since last contact
      if (filter.days_since_last_contact) {
        const cutoffTime = Date.now() - (filter.days_since_last_contact * 24 * 60 * 60 * 1000);
        conditions = q.and(
          conditions,
          q.or(
            q.eq(q.field('client_last_contact'), undefined),
            q.lt(q.field('client_last_contact'), cutoffTime)
          )
        );
      }

      return conditions;
    };

    // Build complete query chains for each case
    let result;
    
    if (filter?.client_status && filter.client_status !== undefined) {
      const baseQuery = ctx.db
        .query('clients')
        .withIndex('by_status', (q) => q.eq('client_status', filter.client_status!));
      
      const filteredQuery = buildFilter ? baseQuery.filter(buildFilter) : baseQuery;
      
      if (pagination?.sortBy === 'name') {
        result = await filteredQuery.order(pagination.sortDirection === 'asc' ? 'asc' : 'desc').paginate(paginationOpts);
      } else {
        result = await filteredQuery.order('desc').paginate(paginationOpts);
      }
    } else if (filter?.client_tier && filter.client_tier !== undefined) {
      const baseQuery = ctx.db
        .query('clients')
        .withIndex('by_tier', (q) => q.eq('client_tier', filter.client_tier!));
      
      const filteredQuery = buildFilter ? baseQuery.filter(buildFilter) : baseQuery;
      
      if (pagination?.sortBy === 'name') {
        result = await filteredQuery.order(pagination.sortDirection === 'asc' ? 'asc' : 'desc').paginate(paginationOpts);
      } else {
        result = await filteredQuery.order('desc').paginate(paginationOpts);
      }
    } else if (filter?.client_type && filter.client_type !== undefined) {
      const baseQuery = ctx.db
        .query('clients')
        .withIndex('by_type', (q) => q.eq('client_type', filter.client_type!));
      
      const filteredQuery = buildFilter ? baseQuery.filter(buildFilter) : baseQuery;
      
      if (pagination?.sortBy === 'name') {
        result = await filteredQuery.order(pagination.sortDirection === 'asc' ? 'asc' : 'desc').paginate(paginationOpts);
      } else {
        result = await filteredQuery.order('desc').paginate(paginationOpts);
      }
    } else {
      const baseQuery = ctx.db.query('clients');
      const filteredQuery = buildFilter ? baseQuery.filter(buildFilter) : baseQuery;
      
      if (pagination?.sortBy === 'name') {
        result = await filteredQuery.order(pagination.sortDirection === 'asc' ? 'asc' : 'desc').paginate(paginationOpts);
      } else {
        result = await filteredQuery.order('desc').paginate(paginationOpts);
      }
    }

    // Enhance page with assignments and badges
    const enhancedPage = await asyncMap(result.page, async (client) => {
      // Fetch assignments and their user details
      const assignments = await ctx.db
        .query('client_assignments')
        .withIndex('by_client_id', (q) => q.eq('client_id', client._id))
        .collect();
      const assignmentsWithUsers = await asyncMap(assignments, async (assignment) => {
        const user = await ctx.db.get(assignment.user_id);
        return {
          assignment,
          user: user ? {
            _id: user._id,
            name: user.name,
            email: user.email,
            avatar: user.image,
          } : null,
        };
      });

      // Fetch badges
      const badges = client.client_badges ? await asyncMap(client.client_badges, (badgeId: any) => ctx.db.get(badgeId)) : [];

      return {
        ...client,
        assignments: assignmentsWithUsers.filter(a => a.user !== null),
        badges: badges.filter(b => b !== null),
      };
    });

    return {
      page: enhancedPage,
      continueCursor: result.continueCursor,
      isDone: result.isDone,
    };
  },
});

// Search clients by text
export const searchClients = zQuery({
  args: {
    searchText: z.string(),
    limit: z.number().optional(),
  },
  handler: async (ctx, args) => {
    const { searchText, limit = 20 } = args;
    const searchLower = searchText.toLowerCase();

    return await ctx.db
      .query('clients')
      .withIndex('by_creation_time')
      .filter((q) => {
        // Simple text search - client side filtering will be more effective for complex text matching
        return q.or(
          q.neq(q.field('client_name'), undefined),
          q.neq(q.field('client_description'), undefined),
          q.neq(q.field('client_research'), undefined)
        );
      })
      .take(limit);
  },
});

// Get clients by status
export const getClientsByStatus = zQuery({
  args: {
    status: ClientStatusSchema,
  },
  handler: async (ctx, args) => {
    return await ctx.db
      .query('clients')
      .withIndex('by_status', (q) => q.eq('client_status', args.status))
      .collect();
  },
});

// Get clients by tier
export const getClientsByTier = zQuery({
  args: {
    tier: ClientTierSchema,
  },
  handler: async (ctx, args) => {
    return await ctx.db
      .query('clients')
      .withIndex('by_tier', (q) => q.eq('client_tier', args.tier))
      .collect();
  },
});

// Get clients by type
export const getClientsByType = zQuery({
  args: {
    type: ClientTypeSchema,
  },
  handler: async (ctx, args) => {
    return await ctx.db
      .query('clients')
      .withIndex('by_type', (q) => q.eq('client_type', args.type))
      .collect();
  },
});

// Client Assignment Queries

// Get all assignments for a client
export const getClientAssignments = zQuery({
  args: {
    clientId: zid('clients'),
  },
  handler: async (ctx, args) => {
    // Get assignments using relationship helper
    const assignments = await ctx.db
      .query('client_assignments')
      .withIndex('by_client_id', (q) => q.eq('client_id', args.clientId))
      .collect();

    // Get user details for each assignment using asyncMap
    return await asyncMap(assignments, async (assignment) => {
      const user = await ctx.db.get(assignment.user_id);
      return {
        assignment,
        user: user ? {
          _id: user._id,
          name: user.name,
          email: user.email,
        } : null,
      };
    });
  },
});

// Get all clients assigned to a user
export const getUserClientAssignments = zQuery({
  args: {
    userId: zid('users'),
  },
  handler: async (ctx, args) => {
    // Get assignments using relationship helper
    const assignments = await ctx.db
      .query('client_assignments')
      .withIndex('by_user_id', (q) => q.eq('user_id', args.userId))
      .collect();

    // Get client details for each assignment using asyncMap
    return await asyncMap(assignments, async (assignment) => {
      const client = await ctx.db.get(assignment.client_id);
      return {
        assignment,
        client,
      };
    });
  },
});

// Get assignments with filters
export const getClientAssignmentsByFilter = zQuery({
  args: ClientAssignmentFilterSchema,
  handler: async (ctx, args) => {
    let assignments;

    if (args.client_id) {
      // Use indexed query for client-based filtering
      assignments = await ctx.db
        .query('client_assignments')
        .withIndex('by_client_id', (q) => q.eq('client_id', args.client_id!))
        .collect();
    } else if (args.user_id) {
      // Use indexed query for user-based filtering
      assignments = await ctx.db
        .query('client_assignments')
        .withIndex('by_user_id', (q) => q.eq('user_id', args.user_id!))
        .collect();
    } else {
      assignments = await ctx.db.query('client_assignments').collect();
    }

    // Get related data using asyncMap for better parallel execution
    return await asyncMap(assignments, async (assignment: any) => {
      const [client, user] = await Promise.all([
        ctx.db.get(assignment.client_id),
        ctx.db.get(assignment.user_id),
      ]);
      return {
        assignment,
        client,
        user: user && 'name' in user && 'email' in user ? {
          _id: user._id,
          name: user.name,
          email: user.email,
        } : null,
      };
    });
  },
});

// Statistics and Analytics Queries

// Get overall client statistics
export const getClientStats = zQuery({
  args: {},
  handler: async (ctx, args) => {
    const allClients = await ctx.db.query('clients').collect();

    const stats = {
      total_clients: allClients.length,
      active_clients: 0,
      prospective_clients: 0,
      clients_by_tier: {
        platinum: 0,
        gold: 0,
        silver: 0,
        bronze: 0,
        none: 0,
      },
      clients_by_status: {
        active: 0,
        on_hold: 0,
        prospective: 0,
        former: 0,
      },
      total_net_worth: 0,
      average_net_worth: 0,
    };

    let totalNetWorth = 0;
    let netWorthCount = 0;

    for (const client of allClients) {
      // Count by status
      const status = client.client_status as keyof typeof stats.clients_by_status;
      if (status in stats.clients_by_status) {
        stats.clients_by_status[status]++;
      }
      if (client.client_status === 'active') {
        stats.active_clients++;
      } else if (client.client_status === 'prospective') {
        stats.prospective_clients++;
      }

      // Count by tier
      const tier = client.client_tier as keyof typeof stats.clients_by_tier;
      if (tier in stats.clients_by_tier) {
        stats.clients_by_tier[tier]++;
      }

      // Calculate net worth totals
      if (client.client_net_worth !== undefined) {
        totalNetWorth += client.client_net_worth;
        netWorthCount++;
      }
    }

    stats.total_net_worth = totalNetWorth;
    stats.average_net_worth = netWorthCount > 0 ? totalNetWorth / netWorthCount : 0;

    return stats;
  },
});

// ========================================
// CLIENT DETAILS PAGE QUERY
// ========================================

export const getClientDetailsPageData = zQuery({
  args: {
    clientId: zid('clients'),
  },
  handler: async (ctx, args) => {
    // 1. Fetch the core client data
    const client = await ctx.db.get(args.clientId);
    if (!client) {
      throw new Error('Client not found');
    }

    // 2. Fetch assigned team members and their titles from the 'people' table
    const assignments = await ctx.db
      .query('client_assignments')
      .withIndex('by_client_id', q => q.eq('client_id', args.clientId))
      .collect();

    const teamMembers = await asyncMap(assignments, async assignment => {
      const user = await ctx.db.get(assignment.user_id);
      if (!user) return null;

      // Fetch the associated person to get the title
      const person = await ctx.db
        .query('people')
        .withIndex('by_userId', q => q.eq('user_id', user._id))
        .unique();

      return {
        assignmentId: assignment._id,
        is_primary_assignment: assignment.is_primary_assignment,
        userId: user._id,
        name: user.name,
        email: user.email,
        image: user.image,
        title: person?.title, // Title comes from the 'people' table
      };
    });

    // 3. Fetch relationships using correct composite indexes
    // TODO: This is a temporary solution to get around the composite index query issue.
    // A more performant solution would be to use a different query pattern.
    const sourceRelationships = await ctx.db
      .query('relationships')
      .withIndex('by_source', q => q.eq('source_type', client.client_type))
      .filter(q => q.eq(q.field('source_id'), args.clientId))
      .collect();

    const targetRelationships = await ctx.db
      .query('relationships')
      .withIndex('by_target', q => q.eq('target_type', client.client_type))
      .filter(q => q.eq(q.field('target_id'), args.clientId))
      .collect();

    const allRelationships = [...sourceRelationships, ...targetRelationships];
    const uniqueRelationships = Array.from(
      new Map(allRelationships.map(r => [r._id, r])).values()
    );

    const relationships = await asyncMap(uniqueRelationships, async rel => {
      const otherEntityId =
        rel.source_id === args.clientId ? rel.target_id : rel.source_id;
      const otherEntityType =
        rel.source_id === args.clientId ? rel.target_type : rel.source_type;

      let relation: any = null;
      // Based on the entity type, query the correct table
      if (otherEntityType === 'person') {
        relation = await ctx.db.get(otherEntityId as any);
      } else if (otherEntityType === 'organization') {
        relation = await ctx.db.get(otherEntityId as any);
      } else if (otherEntityType === 'client') {
        relation = await ctx.db.get(otherEntityId as any);
      }

      const relationshipType = await ctx.db.get(rel.relationship_type_id);

      return relation && relationshipType
        ? {
            relationshipId: rel._id,
            relation: {
              _id: relation._id,
              name: relation.name || relation.client_name, // Use correct name field
              type: otherEntityType,
              description: relation.short_description,
            },
            relationshipType: relationshipType.relationship_name,
            relationshipCategory: relationshipType.relationship_category,
          }
        : null;
    });

    // 4. Fetch active items (TODO: Implement full logic)
    // For now, returning an empty array to fix build errors.
    // A robust implementation would find projects/tasks linked to the client.
    const activeItems: any[] = [];

    // 5. Fetch tags for the client
    const taggings = await ctx.db
      .query('taggings')
      .withIndex('by_taggable', (q) => q.eq('taggable_type', 'client'))
      .filter((q) => q.eq(q.field('taggable_id'), args.clientId))
      .collect();

    const tags = await asyncMap(taggings, async (tagging) => {
      return await ctx.db.get(tagging.tag_id);
    });

    // 6. Find the principal contact
    const principalRelationship = relationships.find(
      r => r?.relationshipCategory === 'professional' // Example logic
    );

    // 7. Assemble the final data structure and validate
    const pageData = {
      client: {
        ...client,
        // Placeholders for contact info not on the base client doc
        primary_contact_email: '<EMAIL>',
        primary_contact_phone: '************',
        office_address: '123 Main St',
      },
      teamMembers: teamMembers.filter(Boolean),
      relationships: relationships.filter(Boolean),
      activeItems,
      principalContact: principalRelationship || undefined,
      tags: tags.filter(Boolean),
    };

    // Validate against the Zod schema before returning
    const validationResult = ClientDetailsPageDataSchema.safeParse(pageData);
    if (!validationResult.success) {
      console.error('Data validation failed:', validationResult.error.flatten());
      throw new Error('Failed to assemble client details data.');
    }

    return validationResult.data;
  },
});


// Get staff workload analysis
export const getStaffWorkload = zQuery({
  args: {},
  handler: async (ctx, args) => {
    // Fetch all data in parallel for better performance
    const [assignments, users, clients] = await Promise.all([
      ctx.db.query('client_assignments').collect(),
      ctx.db.query('users').collect(),
      ctx.db.query('clients').collect(),
    ]);

    // Create lookup maps
    const clientMap = new Map(clients.map(c => [c._id, c]));
    const userMap = new Map(users.map(u => [u._id, u]));

    // Group assignments by user
    const userAssignments = new Map();
    for (const assignment of assignments) {
      if (!userAssignments.has(assignment.user_id)) {
        userAssignments.set(assignment.user_id, []);
      }
      userAssignments.get(assignment.user_id).push(assignment);
    }

    // Calculate workload for each user using asyncMap for better organization
    return await asyncMap(users, async (user) => {
      const userAssignmentList = userAssignments.get(user._id) || [];
      
      let primaryClients = 0;
      let secondaryClients = 0;
      const clientsByTier = {
        platinum: 0,
        gold: 0,
        silver: 0,
        bronze: 0,
        none: 0,
      };

      for (const assignment of userAssignmentList) {
        const client = clientMap.get(assignment.client_id);
        if (client) {
          const tier = client.client_tier as keyof typeof clientsByTier;
          if (tier in clientsByTier) {
            clientsByTier[tier]++;
          }
          // Note: We don't have is_primary_assignment in the schema, 
          // so we'll count all as primary for now
          primaryClients++;
        }
      }

      return {
        user_id: user._id,
        user_name: user.name || user.email,
        primary_clients: primaryClients,
        secondary_clients: secondaryClients,
        total_clients: userAssignmentList.length,
        clients_by_tier: clientsByTier,
      };
    });
  },
});
