import { z } from "zod";
import { zid } from "convex-helpers/server/zod";
import { zCustomMutation, zCustomQuery } from "convex-helpers/server/zod";
import { NoOp } from "convex-helpers/server/customFunctions";
import { internalMutation, internalQuery } from "../_generated/server";
import {
  InternalSaveSignRequestIdArgsSchema,
  GetDocumentSignStatusArgsSchema,
  DocumentSignStatusSchema
} from "./boxSchema";

/**
 * Wrapper around Convex's internalMutation function that uses Zod for validation.
 */
const zInternalMutation = zCustomMutation(internalMutation, NoOp);

/**
 * Wrapper around Convex's internalQuery function that uses Zod for validation.
 */
const zInternalQuery = zCustomQuery(internalQuery, NoOp);

/**
 * internalSaveSignRequestId mutation
 *
 * @description
 * Internal mutation to save Box sign request ID to a document.
 * This runs in V8 isolate (not Node.js) so it can access the database.
 *
 * @example
 * await ctx.runMutation(internal.box.boxSignatureMutations.internalSaveSignRequestId, {
 *   documentId: "j57abc123...",
 *   signRequestId: "box_sign_123..."
 * });
 *
 * @throws {ZodError} If documentId or signRequestId are invalid.
 */
export const internalSaveSignRequestId = zInternalMutation({
    args: {
        documentId: zid("documents"),
        signRequestId: z.string(),
    },
    returns: z.null(),
    handler: async (ctx, args) => {
        const { documentId, signRequestId } = InternalSaveSignRequestIdArgsSchema.parse(args);
        
        // Verify document exists before updating
        const document = await ctx.db.get(documentId);
        if (!document) {
            throw new Error(`Document with ID ${documentId} not found`);
        }
        
        await ctx.db.patch(documentId, {
            box_sign_request_id: signRequestId,
        });
        
        return null;
    },
});

/**
 * getDocumentSignStatus query
 *
 * @description
 * Internal query to get document with sign request status.
 * Returns document sign status information or null if not found.
 *
 * @example
 * const signStatus = await ctx.runQuery(internal.box.boxSignatureMutations.getDocumentSignStatus, {
 *   documentId: "j57abc123..."
 * });
 *
 * @throws {ZodError} If documentId is invalid.
 */
export const getDocumentSignStatus = zInternalQuery({
    args: {
        documentId: zid("documents"),
    },
    returns: DocumentSignStatusSchema,
    handler: async (ctx, args) => {
        const { documentId } = GetDocumentSignStatusArgsSchema.parse(args);
        
        const document = await ctx.db.get(documentId);
        if (!document || !document.box_sign_request_id) {
            return null;
        }
        
        const result = {
            documentId: documentId,
            signRequestId: document.box_sign_request_id,
            fileName: document.name || "Unknown Document",
        };
        
        return DocumentSignStatusSchema.parse(result);
    },
}); 