import { z } from 'zod';
import { zid } from 'convex-helpers/server/zod';

// ========================================
// BOX INTEGRATION CONFIGURATION SCHEMAS
// ========================================

/**
 * Schema for Box Developer Token configuration
 * Used in the user_config field of the integrations table
 */
export const BoxUserConfigSchema = z.object({
  developerToken: z.string(),
  // Optional additional Box configuration
  refreshToken: z.string().optional(),
  accessToken: z.string().optional(),
  tokenExpiry: z.number().optional(),
  clientId: z.string().optional(),
  clientSecret: z.string().optional(),
});

/**
 * Schema for Box expected configuration
 * Defines what configuration fields are expected for Box integration
 */
export const BoxExpectedConfigSchema = z.object({
  developerToken: z.object({
    type: z.literal("string"),
    required: z.boolean(),
    description: z.string(),
  }),
  clientId: z.object({
    type: z.literal("string"),
    required: z.boolean(),
    description: z.string(),
  }).optional(),
});

// ========================================
// BOX INTEGRATION DOCUMENT SCHEMA
// ========================================

/**
 * Complete Box integration document schema
 * Represents the Box integration as stored in the integrations table
 */
export const BoxIntegrationSchema = z.object({
  _id: zid('integrations'),
  _creationTime: z.number(),
  immutable_slug: z.literal('box'),
  status: z.enum(['ACTIVE', 'INACTIVE', 'NEEDS_SETUP', 'ERROR']),
  display_name: z.string().optional(),
  description: z.string().optional(),
  expected_config: BoxExpectedConfigSchema.optional(),
  user_config: BoxUserConfigSchema.optional(),
  updated_at: z.number().optional(),
  last_sync_timestamp: z.number().optional(),
});

// ========================================
// BOX FILE AND FOLDER SCHEMAS
// ========================================

/**
 * Schema for Box file metadata from Box API
 */
export const BoxFileMetadataSchema = z.object({
  id: z.string(),
  name: z.string(),
  size: z.number(),
  type: z.literal('file'),
  created_at: z.string().optional(),
  modified_at: z.string().optional(),
  parent: z.object({
    id: z.string(),
    name: z.string().optional(),
  }).optional(),
  shared_link: z.object({
    url: z.string(),
    download_url: z.string().optional(),
  }).optional(),
});

/**
 * Schema for Box folder metadata from Box API
 */
export const BoxFolderMetadataSchema = z.object({
  id: z.string(),
  name: z.string(),
  type: z.literal('folder'),
  item_count: z.number().optional(),
  created_at: z.string().optional(),
  modified_at: z.string().optional(),
  parent: z.object({
    id: z.string(),
    name: z.string().optional(),
  }).optional(),
});

/**
 * Union schema for Box items (files or folders)
 */
export const BoxItemSchema = z.union([
  BoxFileMetadataSchema,
  BoxFolderMetadataSchema,
]);

// ========================================
// BOX SIGN SCHEMAS
// ========================================

/**
 * Schema for Box Sign request parameters
 */
export const BoxSignRequestSchema = z.object({
  boxFileId: z.string(),
  destinationFolderId: z.string(),
  signerEmail: z.string().email(),
  // Optional Box Sign parameters
  isDocumentPreparationNeeded: z.boolean().optional().default(true),
  redirectUrl: z.string().url().optional(),
  declineRedirectUrl: z.string().url().optional(),
});

/**
 * Schema for Box Sign request response
 */
export const BoxSignResponseSchema = z.object({
  id: z.string(),
  prepare_url: z.string().nullable(),
  status: z.enum(['converting', 'created', 'cancelled', 'completed', 'error']).optional(),
  sign_url: z.string().optional(),
});

/**
 * Schema for Box Sign compatible file types
 */
export const BoxSignFileTypesSchema = z.enum([
  'pdf', 'doc', 'docx', 'rtf', 'txt', 'odt',
  'ppt', 'pptx', 'odp', 'xls', 'xlsx', 'ods', 'csv',
  'png', 'jpg', 'jpeg', 'tiff', 'tif'
]);

// ========================================
// BOX SYNC OPERATION SCHEMAS
// ========================================

/**
 * Schema for Box file sync operation parameters
 */
export const BoxSyncArgsSchema = z.object({
  clientId: zid('clients'),
  folderId: z.string().optional().default('0'), // '0' is root folder
  includeSubfolders: z.boolean().optional().default(true),
});

/**
 * Schema for Box file sync result
 */
export const BoxSyncResultSchema = z.object({
  success: z.boolean(),
  filesProcessed: z.number().optional(),
  filesSkipped: z.number().optional(),
  errors: z.array(z.object({
    fileName: z.string(),
    error: z.string(),
  })).optional(),
  syncTimestamp: z.number(),
});

// ========================================
// CONVEX FILE WITH BOX METADATA SCHEMA
// ========================================

/**
 * Schema for files in Convex that are linked to Box
 */
export const ConvexFileWithBoxSchema = z.object({
  _id: zid('files'),
  _creationTime: z.number(),
  title: z.string().optional(),
  fileName: z.string().optional(),
  description: z.string().optional(),
  docType: z.enum(['KNOWLEDGE_BASE', 'MEETING_NOTES', 'CONTRACT', 'DOCUMENT', 'BILL']),
  box_file_id: z.string(), // Required for Box-linked files
  fileStorageId: zid('_storage').optional(),
  fileSize: z.number().optional(),
  fileExtension: z.string().optional(),
  updated_at: z.number().optional(),
});

// ========================================
// HELPER FUNCTIONS AND UTILITIES
// ========================================

/**
 * List of file extensions supported by Box Sign
 */
export const BOX_SIGN_SUPPORTED_EXTENSIONS = [
  'pdf', 'doc', 'docx', 'rtf', 'txt', 'odt',
  'ppt', 'pptx', 'odp', 'xls', 'xlsx', 'ods', 'csv',
  'png', 'jpg', 'jpeg', 'tiff', 'tif'
] as const;

/**
 * Content type mapping for Box file extensions
 */
export const BOX_CONTENT_TYPE_MAP: Record<string, string> = {
  'pdf': 'application/pdf',
  'doc': 'application/msword',
  'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'xls': 'application/vnd.ms-excel',
  'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'ppt': 'application/vnd.ms-powerpoint',
  'pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
  'txt': 'text/plain',
  'jpg': 'image/jpeg',
  'jpeg': 'image/jpeg',
  'png': 'image/png',
  'gif': 'image/gif',
  'rtf': 'application/rtf',
  'odt': 'application/vnd.oasis.opendocument.text',
  'odp': 'application/vnd.oasis.opendocument.presentation',
  'ods': 'application/vnd.oasis.opendocument.spreadsheet',
  'csv': 'text/csv',
  'tiff': 'image/tiff',
  'tif': 'image/tiff',
};

// ========================================
// BOX ACTIONS SCHEMAS
// ========================================

/**
 * Schema for convertAndSaveMarkdown action arguments
 */
export const ConvertAndSaveMarkdownArgsSchema = z.object({
  documentId: zid("documents"),
  storageId: zid("_storage"),
});

/**
 * Schema for sendForSignature action arguments
 */
export const SendForSignatureArgsSchema = z.object({
  documentId: zid("documents"),
  signerEmail: z.string().email(),
});

/**
 * Schema for getOrCreateSignRequestsFolder action arguments
 */
export const GetOrCreateSignRequestsFolderArgsSchema = z.object({
  sourceFileId: z.string(),
});

/**
 * Schema for createSignRequest action arguments
 */
export const CreateSignRequestArgsSchema = z.object({
  boxFileId: z.string(),
  destinationFolderId: z.string(), 
  signerEmail: z.string().email(),
});

/**
 * Schema for getSignRequestStatus action arguments
 */
export const GetSignRequestStatusArgsSchema = z.object({
  signRequestId: z.string(),
});

/**
 * Schema for fetchSignRequest action arguments
 */
export const FetchSignRequestArgsSchema = z.object({
  signRequestId: z.string(),
});

/**
 * Schema for Box Sign request response
 */
export const SignRequestResponseSchema = z.object({
  id: z.string(),
  prepare_url: z.string().nullable(),
});

/**
 * Schema for internalSaveSignRequestId mutation arguments
 */
export const InternalSaveSignRequestIdArgsSchema = z.object({
  documentId: zid("documents"),
  signRequestId: z.string(),
});

/**
 * Schema for getDocumentSignStatus query arguments
 */
export const GetDocumentSignStatusArgsSchema = z.object({
  documentId: zid("documents"),
});

/**
 * Schema for getDocumentSignStatus query return value
 */
export const DocumentSignStatusSchema = z.object({
  documentId: zid("documents"),
  signRequestId: z.string(),
  fileName: z.string(),
}).nullable();

// ========================================
// TYPE EXPORTS
// ========================================

export type BoxUserConfig = z.infer<typeof BoxUserConfigSchema>;
export type BoxExpectedConfig = z.infer<typeof BoxExpectedConfigSchema>;
export type BoxIntegration = z.infer<typeof BoxIntegrationSchema>;
export type BoxFileMetadata = z.infer<typeof BoxFileMetadataSchema>;
export type BoxFolderMetadata = z.infer<typeof BoxFolderMetadataSchema>;
export type BoxItem = z.infer<typeof BoxItemSchema>;
export type BoxSignRequest = z.infer<typeof BoxSignRequestSchema>;
export type BoxSignResponse = z.infer<typeof BoxSignResponseSchema>;
export type BoxSyncArgs = z.infer<typeof BoxSyncArgsSchema>;
export type BoxSyncResult = z.infer<typeof BoxSyncResultSchema>;
export type ConvexFileWithBox = z.infer<typeof ConvexFileWithBoxSchema>;
export type BoxSignFileType = z.infer<typeof BoxSignFileTypesSchema>;
export type ConvertAndSaveMarkdownArgs = z.infer<typeof ConvertAndSaveMarkdownArgsSchema>;
export type SendForSignatureArgs = z.infer<typeof SendForSignatureArgsSchema>;
export type GetOrCreateSignRequestsFolderArgs = z.infer<typeof GetOrCreateSignRequestsFolderArgsSchema>;
export type CreateSignRequestArgs = z.infer<typeof CreateSignRequestArgsSchema>;
export type GetSignRequestStatusArgs = z.infer<typeof GetSignRequestStatusArgsSchema>;
export type FetchSignRequestArgs = z.infer<typeof FetchSignRequestArgsSchema>;
export type SignRequestResponse = z.infer<typeof SignRequestResponseSchema>;
export type InternalSaveSignRequestIdArgs = z.infer<typeof InternalSaveSignRequestIdArgsSchema>;
export type GetDocumentSignStatusArgs = z.infer<typeof GetDocumentSignStatusArgsSchema>;
export type DocumentSignStatus = z.infer<typeof DocumentSignStatusSchema>;

 