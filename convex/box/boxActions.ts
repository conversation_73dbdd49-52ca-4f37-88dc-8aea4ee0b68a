"use node";

import { z } from "zod";
import { zid } from "convex-helpers/server/zod";
import { internal } from "../_generated/api";
import { zCustomAction } from "convex-helpers/server/zod";
import { NoOp } from "convex-helpers/server/customFunctions";
import { internalAction } from "../_generated/server";

/**
 * A wrapper around Convex's internalAction function that uses Zod for validation.
 * Provides better type safety and more flexible validation.
 */
const zAction = zCustomAction(internalAction, NoOp);

/**
 * convertAndSaveMarkdown action
 *
 * @description
 * Converts a document from storage and saves it as markdown using the LLM-friendly converter.
 * This action processes uploaded files and makes them accessible in a markdown format.
 *
 * @example
 * await ctx.runAction(internal.box.boxActions.convertAndSaveMarkdown, {
 *   documentId: "j57abc123...",
 *   storageId: "kg4def456..."
 * });
 *
 * @throws {ZodError} If documentId or storageId are invalid.
 */
export const convertAndSaveMarkdown = zAction({
  args: {
    documentId: zid("documents"),
    storageId: zid("_storage"),
  },
  returns: z.null(),
  handler: async (ctx, args) => {
    // Args are already validated by zAction wrapper
    const { documentId, storageId } = args;

    // Call the LLM-friendly converter action with the correct path
    await ctx.runAction(internal.ai.llmFriendlyConverter.llmFriendlyConverter, {
      documentId,
      source: {
        type: "storage",
        storageId,
      },
    });

    return null;
  },
});
